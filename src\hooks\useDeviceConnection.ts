import { useState, useCallback, useEffect } from 'react';
import { DeviceConnection, DeviceStatus } from '../types';

export function useDeviceConnection() {
  const [connections, setConnections] = useState<DeviceConnection[]>([]);
  const [activeConnection, setActiveConnection] = useState<DeviceConnection | null>(null);
  const [deviceStatus, setDeviceStatus] = useState<DeviceStatus | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);

  const connect = useCallback(async (connection: Omit<DeviceConnection, 'id' | 'status'>) => {
    setIsConnecting(true);
    
    try {
      // Simulate connection process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newConnection: DeviceConnection = {
        ...connection,
        id: Date.now().toString(),
        status: 'connected',
        lastConnected: new Date(),
      };

      setConnections(prev => [...prev, newConnection]);
      setActiveConnection(newConnection);
      
      // Simulate device status
      setDeviceStatus({
        temperature: 25.5,
        voltage: 230.2,
        current: 0.5,
        power: 115.1,
        isReady: true,
        errorCount: 0,
      });

      return newConnection;
    } catch (error) {
      throw new Error('Failed to connect to device');
    } finally {
      setIsConnecting(false);
    }
  }, []);

  const disconnect = useCallback((connectionId: string) => {
    setConnections(prev => 
      prev.map(conn => 
        conn.id === connectionId 
          ? { ...conn, status: 'disconnected' as const }
          : conn
      )
    );
    
    if (activeConnection?.id === connectionId) {
      setActiveConnection(null);
      setDeviceStatus(null);
    }
  }, [activeConnection]);

  const removeConnection = useCallback((connectionId: string) => {
    setConnections(prev => prev.filter(conn => conn.id !== connectionId));
    
    if (activeConnection?.id === connectionId) {
      setActiveConnection(null);
      setDeviceStatus(null);
    }
  }, [activeConnection]);

  // Simulate periodic status updates
  useEffect(() => {
    if (!activeConnection || activeConnection.status !== 'connected') return;

    const interval = setInterval(() => {
      setDeviceStatus(prev => prev ? {
        ...prev,
        temperature: 25 + Math.random() * 5,
        voltage: 230 + Math.random() * 10 - 5,
        current: 0.3 + Math.random() * 0.4,
        power: 110 + Math.random() * 20,
      } : null);
    }, 3000);

    return () => clearInterval(interval);
  }, [activeConnection]);

  return {
    connections,
    activeConnection,
    deviceStatus,
    isConnecting,
    connect,
    disconnect,
    removeConnection,
    setActiveConnection,
  };
}