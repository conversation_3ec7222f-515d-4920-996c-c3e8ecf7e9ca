/**
 * CWG_Properties.ts - Device module specific commands for "CWG" test parameters
 * Equivalent to CWG_Properties.py in the original project
 * Contains CWG-specific parameter definitions and validation
 */

export interface CWGParameters {
  level: number;           // Voltage level in V
  frequency?: number;      // Frequency in Hz
  waveform?: string;       // Waveform type
  duration?: number;       // Test duration in seconds
  modulation?: string;     // Modulation type
  sweepMode?: string;      // Sweep configuration
}

export interface CWGLimits {
  minLevel: number;
  maxLevel: number;
  minFrequency: number;
  maxFrequency: number;
  availableWaveforms: string[];
  availableModulations: string[];
}

export class CWGProperties {
  private static readonly DEFAULT_LIMITS: CWGLimits = {
    minLevel: 0,
    maxLevel: 5000,
    minFrequency: 0.1,
    maxFrequency: 1000,
    availableWaveforms: ['SINE', 'SQUARE', 'TRIANGLE', 'SAWTOOTH'],
    availableModulations: ['NONE', 'AM', 'FM', 'PM'],
  };

  /**
   * Get CWG parameter limits
   */
  static getLimits(): CWGLimits {
    return { ...this.DEFAULT_LIMITS };
  }

  /**
   * Validate CWG parameters
   */
  static validateParameters(params: CWGParameters): string[] {
    const errors: string[] = [];
    const limits = this.getLimits();

    // Validate level
    if (params.level < limits.minLevel || params.level > limits.maxLevel) {
      errors.push(`Level must be between ${limits.minLevel}V and ${limits.maxLevel}V`);
    }

    // Validate frequency
    if (params.frequency !== undefined) {
      if (params.frequency < limits.minFrequency || params.frequency > limits.maxFrequency) {
        errors.push(`Frequency must be between ${limits.minFrequency}Hz and ${limits.maxFrequency}Hz`);
      }
    }

    // Validate waveform
    if (params.waveform && !limits.availableWaveforms.includes(params.waveform)) {
      errors.push(`Waveform must be one of: ${limits.availableWaveforms.join(', ')}`);
    }

    // Validate modulation
    if (params.modulation && !limits.availableModulations.includes(params.modulation)) {
      errors.push(`Modulation must be one of: ${limits.availableModulations.join(', ')}`);
    }

    return errors;
  }

  /**
   * Get default CWG parameters
   */
  static getDefaultParameters(): CWGParameters {
    return {
      level: 1000,
      frequency: 1,
      waveform: 'SINE',
      duration: 60,
      modulation: 'NONE',
    };
  }

  /**
   * Convert CWG parameters to SCPI commands
   */
  static parametersToCommands(params: CWGParameters): string[] {
    const commands: string[] = [];

    // Set test type to CWG
    commands.push('TEST:ACTIVE CWG');

    // Set level
    commands.push(`TEST:CWG:LEVEL:VAL ${params.level}`);

    // Set frequency if specified
    if (params.frequency !== undefined) {
      commands.push(`TEST:CWG:FREQUENCY:VAL ${params.frequency}`);
    }

    // Set waveform if specified
    if (params.waveform) {
      commands.push(`TEST:CWG:WAVEFORM ${params.waveform}`);
    }

    // Set duration if specified
    if (params.duration !== undefined) {
      commands.push(`TEST:CWG:DURATION:VAL ${params.duration}`);
    }

    // Set modulation if specified
    if (params.modulation && params.modulation !== 'NONE') {
      commands.push(`TEST:CWG:MODULATION ${params.modulation}`);
    }

    return commands;
  }

  /**
   * Parse CWG parameters from device response
   */
  static parseFromDevice(lrnResponse: string): CWGParameters {
    const params: CWGParameters = this.getDefaultParameters();
    
    // Parse LRN response to extract CWG parameters
    const commands = lrnResponse.split(';');
    
    for (const command of commands) {
      const [cmd, value] = command.split(' ');
      
      switch (cmd) {
        case 'TEST:CWG:LEVEL:VAL':
          params.level = parseFloat(value);
          break;
        case 'TEST:CWG:FREQUENCY:VAL':
          params.frequency = parseFloat(value);
          break;
        case 'TEST:CWG:WAVEFORM':
          params.waveform = value;
          break;
        case 'TEST:CWG:DURATION:VAL':
          params.duration = parseFloat(value);
          break;
        case 'TEST:CWG:MODULATION':
          params.modulation = value;
          break;
      }
    }

    return params;
  }

  /**
   * Get parameter info for UI display
   */
  static getParameterInfo() {
    return {
      level: {
        name: 'Voltage Level',
        unit: 'V',
        description: 'Output voltage level for CWG test',
        type: 'number',
        min: this.DEFAULT_LIMITS.minLevel,
        max: this.DEFAULT_LIMITS.maxLevel,
      },
      frequency: {
        name: 'Frequency',
        unit: 'Hz',
        description: 'Signal frequency',
        type: 'number',
        min: this.DEFAULT_LIMITS.minFrequency,
        max: this.DEFAULT_LIMITS.maxFrequency,
      },
      waveform: {
        name: 'Waveform',
        unit: '',
        description: 'Signal waveform type',
        type: 'select',
        options: this.DEFAULT_LIMITS.availableWaveforms,
      },
      duration: {
        name: 'Duration',
        unit: 's',
        description: 'Test duration in seconds',
        type: 'number',
        min: 1,
        max: 3600,
      },
      modulation: {
        name: 'Modulation',
        unit: '',
        description: 'Signal modulation type',
        type: 'select',
        options: this.DEFAULT_LIMITS.availableModulations,
      },
    };
  }
}