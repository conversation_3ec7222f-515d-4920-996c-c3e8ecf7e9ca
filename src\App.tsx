import React, { useState } from 'react';
import { Header } from './components/layout/Header';
import { Sidebar } from './components/layout/Sidebar';
import { DeviceConnection } from './components/connection/DeviceConnection';
import { DeviceMonitor } from './components/monitoring/DeviceMonitor';
import { TestProfiles } from './components/profiles/TestProfiles';
import { TestExecution } from './components/execution/TestExecution';
import { TestHistory } from './components/history/TestHistory';
import { CustomParameters } from './components/parameters/CustomParameters';
import { SCPICommands } from './components/commands/SCPICommands';
import { CMI } from './components/interface/CMI';

function App() {
  const [activeTab, setActiveTab] = useState('cmi');
  const [currentUser] = useState({
    name: '<PERSON>',
    role: 'engineer' as const,
  });
  const [interfaceMode, setInterfaceMode] = useState<'basic' | 'advanced'>('basic');

  const renderContent = () => {
    switch (activeTab) {
      case 'cmi':
        return (
          <CMI 
            userRole={currentUser.role} 
            onModeChange={setInterfaceMode}
          />
        );
      case 'connection':
        return <DeviceConnection />;
      case 'monitoring':
        return <DeviceMonitor />;
      case 'profiles':
        return <TestProfiles />;
      case 'execution':
        return <TestExecution />;
      case 'history':
        return <TestHistory />;
      case 'parameters':
        return <CustomParameters />;
      case 'commands':
        return <SCPICommands />;
      case 'analytics':
        return (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <p className="text-lg font-medium">Analytics</p>
              <p className="text-sm">Test analytics and reporting coming soon</p>
            </div>
          </div>
        );
      default:
        return (
          <CMI 
            userRole={currentUser.role} 
            onModeChange={setInterfaceMode}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header
        currentUser={currentUser}
        onSettingsClick={() => console.log('Settings clicked')}
        onHelpClick={() => console.log('Help clicked')}
      />
      
      <div className="flex flex-1">
        <Sidebar
          activeTab={activeTab}
          onTabChange={setActiveTab}
          userRole={currentUser.role}
          interfaceMode={interfaceMode}
        />
        
        <main className="flex-1 p-6 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {renderContent()}
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;