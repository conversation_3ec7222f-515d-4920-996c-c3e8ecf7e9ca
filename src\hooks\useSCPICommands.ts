import { useState, useCallback } from 'react';

export interface SCPICommandEntry {
  id: string;
  command: string;
  response?: string;
  error?: string;
  timestamp: Date;
  status: 'success' | 'error' | 'info';
  executionTime: number;
}

export function useSCPICommands() {
  const [commandHistory, setCommandHistory] = useState<SCPICommandEntry[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);

  const executeCommand = useCallback(async (command: string): Promise<SCPICommandEntry> => {
    setIsExecuting(true);
    const startTime = Date.now();
    
    const entry: SCPICommandEntry = {
      id: Date.now().toString(),
      command,
      timestamp: new Date(),
      status: 'info',
      executionTime: 0,
    };

    try {
      // Simulate command execution with realistic responses
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 200));
      
      const response = simulateCommandResponse(command);
      const executionTime = Date.now() - startTime;
      
      const completedEntry: SCPICommandEntry = {
        ...entry,
        response,
        status: 'success',
        executionTime,
      };

      setCommandHistory(prev => [...prev, completedEntry]);
      return completedEntry;
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorEntry: SCPICommandEntry = {
        ...entry,
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 'error',
        executionTime,
      };

      setCommandHistory(prev => [...prev, errorEntry]);
      throw error;
    } finally {
      setIsExecuting(false);
    }
  }, []);

  const clearHistory = useCallback(() => {
    setCommandHistory([]);
  }, []);

  const getCommandSuggestions = useCallback((input: string): string[] => {
    const allCommands = [
      '*IDN?', '*LRN?', 'PUSH?', 'PUSH ENABLED', 'PUSH DISABLED',
      'TEST:AVAIL?', 'TEST:ACTIVE?', 'TEST:ACTIVE CWG', 'TEST:ACTIVE EFT',
      'RUN:STATE?', 'RUN:START', 'RUN:STOP', 'RUN:TRIGGER:TRIGGER',
      'RUN:TIME:TOTAL?', 'RUN:TIME:CURRENT?', 'RUN:ERROR?', 'RUN:WARNING?',
      'PWR:SET?', 'PWR:SET OFF', 'PWR:SET PWR1', 'PWR:SET PWR2',
      'PWR:MEAS:1:VOLTAGE?', 'PWR:MEAS:1:CURRENT?', 'PWR:SAFETY_CIRCUIT?',
      'SYSTEM:INFO:VERSION?', 'SYSTEM:INFO:HARDWARE_TYPE?', 'SYSTEM:INFO:SERIAL?',
      'ENVIRONMENT:TEMPERATURE?', 'ENVIRONMENT:HUMIDITY?', 'ENVIRONMENT:UNIT_TEMP?',
      'PROTOCOL:GENERAL:COMPANY?', 'PROTOCOL:GENERAL:OPERATOR?', 'PROTOCOL:CREATE?',
      'AUDIO:PLAY BEEP', 'AUDIO:SOUNDS:READY_TRIGGER?',
    ];

    return allCommands.filter(cmd => 
      cmd.toLowerCase().includes(input.toLowerCase())
    ).slice(0, 10);
  }, []);

  return {
    commandHistory,
    isExecuting,
    executeCommand,
    clearHistory,
    getCommandSuggestions,
  };
}

function simulateCommandResponse(command: string): string {
  const cmd = command.toUpperCase().trim();
  
  // Identification commands
  if (cmd === '*IDN?') {
    return '"EMC Partner,IMU-3000,SN123456,EPOS v2.1.0"';
  }
  
  if (cmd === '*LRN?') {
    return 'TEST:ACTIVE CWG;TEST:CWG:LEVEL:VAL 1000;PWR:SET PWR1;PROTOCOL:CREATE YES';
  }
  
  // Test commands
  if (cmd === 'TEST:AVAIL?') {
    return 'CWG,VAR,DIPS_AC,DIPS_DC,CM,EFT,RINGWAVE,TELECOM,ESD,MF';
  }
  
  if (cmd === 'TEST:ACTIVE?') {
    return 'CWG';
  }
  
  if (cmd.startsWith('TEST:ACTIVE ')) {
    return 'OK';
  }
  
  // Run commands
  if (cmd === 'RUN:STATE?') {
    return Math.random() > 0.5 ? 'IDLE' : 'RUNNING';
  }
  
  if (cmd === 'RUN:START' || cmd === 'RUN:STOP') {
    return 'OK';
  }
  
  if (cmd === 'RUN:TIME:TOTAL?') {
    return '120.5 s';
  }
  
  if (cmd === 'RUN:TIME:CURRENT?') {
    return '45.2 s';
  }
  
  if (cmd === 'RUN:ERROR?') {
    return Math.random() > 0.8 ? 'TIMEOUT_ERROR' : 'NO_ERROR';
  }
  
  if (cmd === 'RUN:WARNING?') {
    return 'NO_WARNING';
  }
  
  // Power commands
  if (cmd === 'PWR:SET?') {
    return 'PWR1';
  }
  
  if (cmd.startsWith('PWR:SET ')) {
    return 'OK';
  }
  
  if (cmd.includes('PWR:MEAS:') && cmd.includes(':VOLTAGE?')) {
    return `${(230 + Math.random() * 10 - 5).toFixed(1)} V`;
  }
  
  if (cmd.includes('PWR:MEAS:') && cmd.includes(':CURRENT?')) {
    return `${(0.5 + Math.random() * 0.3).toFixed(2)} A`;
  }
  
  if (cmd === 'PWR:SAFETY_CIRCUIT?') {
    return 'OK';
  }
  
  // System commands
  if (cmd === 'SYSTEM:INFO:VERSION?') {
    return '"EPOS v2.1.0"';
  }
  
  if (cmd === 'SYSTEM:INFO:HARDWARE_TYPE?') {
    return '"IMU-3000"';
  }
  
  if (cmd === 'SYSTEM:INFO:SERIAL?') {
    return '"SN123456"';
  }
  
  if (cmd === 'SYSTEM:INFO:NAME?') {
    return '"EMC Partner IMU-3000"';
  }
  
  // Environment commands
  if (cmd === 'ENVIRONMENT:TEMPERATURE?') {
    return `${(25 + Math.random() * 5).toFixed(1)} C`;
  }
  
  if (cmd === 'ENVIRONMENT:HUMIDITY?') {
    return `${(45 + Math.random() * 10).toFixed(1)} %`;
  }
  
  if (cmd === 'ENVIRONMENT:UNIT_TEMP?') {
    return 'C';
  }
  
  // Protocol commands
  if (cmd === 'PROTOCOL:GENERAL:COMPANY?') {
    return '"Schweitzer Engineering Laboratories"';
  }
  
  if (cmd === 'PROTOCOL:GENERAL:OPERATOR?') {
    return '"David Gonzalez"';
  }
  
  if (cmd === 'PROTOCOL:CREATE?') {
    return 'YES';
  }
  
  // Audio commands
  if (cmd.startsWith('AUDIO:PLAY ')) {
    return 'OK';
  }
  
  if (cmd.includes('AUDIO:SOUNDS:')) {
    return 'BEEP';
  }
  
  // Push notifications
  if (cmd === 'PUSH?') {
    return 'ENABLED';
  }
  
  if (cmd.startsWith('PUSH ')) {
    return 'OK';
  }
  
  // Default response for unrecognized commands
  if (cmd.endsWith('?')) {
    return 'UNKNOWN_QUERY';
  } else {
    return 'OK';
  }
}