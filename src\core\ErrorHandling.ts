/**
 * ErrorHandling.ts - Centralized error handling
 * Equivalent to error_handling.py in the original project
 * Class focused on error handling and logging
 */

export enum ErrorSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

export interface ErrorEntry {
  id: string;
  timestamp: Date;
  severity: ErrorSeverity;
  category: string;
  message: string;
  details?: string;
  source?: string;
  resolved: boolean;
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errors: ErrorEntry[] = [];
  private maxErrors: number = 1000;

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Log an error
   */
  logError(
    severity: ErrorSeverity,
    category: string,
    message: string,
    details?: string,
    source?: string
  ): string {
    const errorId = Date.now().toString();
    
    const error: ErrorEntry = {
      id: errorId,
      timestamp: new Date(),
      severity,
      category,
      message,
      details,
      source,
      resolved: false,
    };

    this.errors.unshift(error);

    // Maintain max error limit
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }

    // Log to console based on severity
    this.logToConsole(error);

    return errorId;
  }

  /**
   * Log device communication error
   */
  logDeviceError(command: string, error: string, details?: string): string {
    return this.logError(
      ErrorSeverity.ERROR,
      'DEVICE_COMMUNICATION',
      `Command failed: ${command}`,
      `Error: ${error}${details ? `\nDetails: ${details}` : ''}`,
      'GeneratorBase'
    );
  }

  /**
   * Log test execution error
   */
  logTestError(testName: string, error: string, details?: string): string {
    return this.logError(
      ErrorSeverity.ERROR,
      'TEST_EXECUTION',
      `Test failed: ${testName}`,
      `Error: ${error}${details ? `\nDetails: ${details}` : ''}`,
      'TestManager'
    );
  }

  /**
   * Log parameter validation error
   */
  logValidationError(parameter: string, value: any, reason: string): string {
    return this.logError(
      ErrorSeverity.WARNING,
      'PARAMETER_VALIDATION',
      `Invalid parameter: ${parameter}`,
      `Value: ${value}, Reason: ${reason}`,
      'ParameterValidation'
    );
  }

  /**
   * Log connection error
   */
  logConnectionError(address: string, port: number, error: string): string {
    return this.logError(
      ErrorSeverity.CRITICAL,
      'CONNECTION',
      `Connection failed to ${address}:${port}`,
      error,
      'DeviceConnection'
    );
  }

  /**
   * Mark error as resolved
   */
  resolveError(errorId: string): boolean {
    const error = this.errors.find(e => e.id === errorId);
    if (error) {
      error.resolved = true;
      return true;
    }
    return false;
  }

  /**
   * Get all errors
   */
  getAllErrors(): ErrorEntry[] {
    return [...this.errors];
  }

  /**
   * Get errors by severity
   */
  getErrorsBySeverity(severity: ErrorSeverity): ErrorEntry[] {
    return this.errors.filter(e => e.severity === severity);
  }

  /**
   * Get unresolved errors
   */
  getUnresolvedErrors(): ErrorEntry[] {
    return this.errors.filter(e => !e.resolved);
  }

  /**
   * Get errors by category
   */
  getErrorsByCategory(category: string): ErrorEntry[] {
    return this.errors.filter(e => e.category === category);
  }

  /**
   * Clear all errors
   */
  clearErrors(): void {
    this.errors = [];
  }

  /**
   * Clear resolved errors
   */
  clearResolvedErrors(): void {
    this.errors = this.errors.filter(e => !e.resolved);
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byCategory: Record<string, number>;
    bySeverity: Record<ErrorSeverity, number>;
    unresolved: number;
  } {
    const stats = {
      total: this.errors.length,
      byCategory: {} as Record<string, number>,
      bySeverity: {} as Record<ErrorSeverity, number>,
      unresolved: this.errors.filter(e => !e.resolved).length,
    };

    // Count by category
    this.errors.forEach(error => {
      stats.byCategory[error.category] = (stats.byCategory[error.category] || 0) + 1;
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
    });

    return stats;
  }

  /**
   * Log to console based on severity
   */
  private logToConsole(error: ErrorEntry): void {
    const message = `[${error.severity.toUpperCase()}] ${error.category}: ${error.message}`;
    
    switch (error.severity) {
      case ErrorSeverity.INFO:
        console.info(message, error.details);
        break;
      case ErrorSeverity.WARNING:
        console.warn(message, error.details);
        break;
      case ErrorSeverity.ERROR:
        console.error(message, error.details);
        break;
      case ErrorSeverity.CRITICAL:
        console.error(`🚨 CRITICAL: ${message}`, error.details);
        break;
    }
  }

  /**
   * Export errors to CSV format
   */
  exportToCSV(): string {
    const headers = ['Timestamp', 'Severity', 'Category', 'Message', 'Details', 'Source', 'Resolved'];
    const rows = this.errors.map(error => [
      error.timestamp.toISOString(),
      error.severity,
      error.category,
      error.message,
      error.details || '',
      error.source || '',
      error.resolved.toString(),
    ]);

    return [headers, ...rows].map(row => 
      row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
    ).join('\n');
  }
}

// Convenience functions for common error types
export const logDeviceError = (command: string, error: string, details?: string) =>
  ErrorHandler.getInstance().logDeviceError(command, error, details);

export const logTestError = (testName: string, error: string, details?: string) =>
  ErrorHandler.getInstance().logTestError(testName, error, details);

export const logValidationError = (parameter: string, value: any, reason: string) =>
  ErrorHandler.getInstance().logValidationError(parameter, value, reason);

export const logConnectionError = (address: string, port: number, error: string) =>
  ErrorHandler.getInstance().logConnectionError(address, port, error);