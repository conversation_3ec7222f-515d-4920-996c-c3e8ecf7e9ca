import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Badge } from '../ui/Badge';
import { Settings, Save, RotateCcw, AlertTriangle, Info } from 'lucide-react';
import { useDeviceConnection } from '../../hooks/useDeviceConnection';
import { useSCPICommands } from '../../hooks/useSCPICommands';

interface ParameterGroup {
  id: string;
  name: string;
  description: string;
  parameters: Parameter[];
}

interface Parameter {
  id: string;
  name: string;
  command: string;
  type: 'number' | 'select' | 'boolean' | 'string';
  value: any;
  unit?: string;
  min?: number;
  max?: number;
  options?: Array<{ value: string; label: string }>;
  description: string;
  readonly?: boolean;
}

export function CustomParameters() {
  const { activeConnection } = useDeviceConnection();
  const { executeCommand } = useSCPICommands();
  const [selectedGroup, setSelectedGroup] = useState('test');
  const [parameters, setParameters] = useState<ParameterGroup[]>(getDefaultParameterGroups());
  const [hasChanges, setHasChanges] = useState(false);
  const [isApplying, setIsApplying] = useState(false);

  const handleParameterChange = (groupId: string, parameterId: string, value: any) => {
    setParameters(prev => prev.map(group => 
      group.id === groupId 
        ? {
            ...group,
            parameters: group.parameters.map(param =>
              param.id === parameterId ? { ...param, value } : param
            )
          }
        : group
    ));
    setHasChanges(true);
  };

  const applyChanges = async () => {
    if (!activeConnection) return;
    
    setIsApplying(true);
    try {
      const currentGroup = parameters.find(g => g.id === selectedGroup);
      if (!currentGroup) return;

      for (const param of currentGroup.parameters) {
        if (!param.readonly && param.command) {
          let command = param.command;
          if (!command.endsWith('?')) {
            command += ` ${param.value}`;
          }
          await executeCommand(command);
        }
      }
      
      setHasChanges(false);
    } catch (error) {
      console.error('Failed to apply parameters:', error);
    } finally {
      setIsApplying(false);
    }
  };

  const resetToDefaults = () => {
    setParameters(getDefaultParameterGroups());
    setHasChanges(false);
  };

  const currentGroup = parameters.find(g => g.id === selectedGroup);

  if (!activeConnection) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-gray-500">
          <Settings className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No active device connection</p>
          <p className="text-sm">Connect to a device before configuring parameters</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Custom Parameters</h2>
          <p className="text-sm text-gray-600">
            Advanced parameter configuration for {activeConnection.name}
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={resetToDefaults}
            disabled={!hasChanges}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={applyChanges}
            disabled={!hasChanges}
            isLoading={isApplying}
          >
            <Save className="w-4 h-4 mr-2" />
            Apply Changes
          </Button>
        </div>
      </div>

      {hasChanges && (
        <div className="bg-warning-50 border border-warning-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-5 h-5 text-warning-600" />
            <div>
              <p className="text-sm font-medium text-warning-900">Unsaved Changes</p>
              <p className="text-sm text-warning-700">
                You have modified parameters that haven't been applied to the device yet.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div>
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">Parameter Groups</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {parameters.map((group) => (
                  <button
                    key={group.id}
                    onClick={() => setSelectedGroup(group.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      selectedGroup === group.id
                        ? 'bg-primary-100 text-primary-700 border border-primary-200'
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                  >
                    <div className="font-medium">{group.name}</div>
                    <div className="text-sm text-gray-500 mt-1">{group.description}</div>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-3">
          {currentGroup && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{currentGroup.name}</h3>
                    <p className="text-sm text-gray-600">{currentGroup.description}</p>
                  </div>
                  <Badge variant="info">
                    {currentGroup.parameters.length} parameters
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {currentGroup.parameters.map((param) => (
                    <div key={param.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <label className="block text-sm font-medium text-gray-900">
                            {param.name}
                            {param.unit && <span className="text-gray-500 ml-1">({param.unit})</span>}
                          </label>
                          <p className="text-sm text-gray-600 mt-1">{param.description}</p>
                          {param.command && (
                            <code className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded mt-1 inline-block">
                              {param.command}
                            </code>
                          )}
                        </div>
                        {param.readonly && (
                          <Badge variant="default">Read Only</Badge>
                        )}
                      </div>

                      <div className="mt-2">
                        {param.type === 'number' && (
                          <Input
                            type="number"
                            value={param.value}
                            onChange={(e) => handleParameterChange(currentGroup.id, param.id, parseFloat(e.target.value))}
                            min={param.min}
                            max={param.max}
                            disabled={param.readonly}
                            className="max-w-xs"
                          />
                        )}

                        {param.type === 'select' && param.options && (
                          <Select
                            value={param.value}
                            onChange={(e) => handleParameterChange(currentGroup.id, param.id, e.target.value)}
                            options={param.options}
                            disabled={param.readonly}
                            className="max-w-xs"
                          />
                        )}

                        {param.type === 'boolean' && (
                          <Select
                            value={param.value ? 'true' : 'false'}
                            onChange={(e) => handleParameterChange(currentGroup.id, param.id, e.target.value === 'true')}
                            options={[
                              { value: 'true', label: 'Enabled' },
                              { value: 'false', label: 'Disabled' },
                            ]}
                            disabled={param.readonly}
                            className="max-w-xs"
                          />
                        )}

                        {param.type === 'string' && (
                          <Input
                            type="text"
                            value={param.value}
                            onChange={(e) => handleParameterChange(currentGroup.id, param.id, e.target.value)}
                            disabled={param.readonly}
                            className="max-w-md"
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <div className="bg-info-50 border border-info-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-info-600 mt-0.5" />
          <div>
            <p className="text-sm font-medium text-info-900">Parameter Configuration Tips</p>
            <ul className="text-sm text-info-700 mt-1 space-y-1">
              <li>• Changes are applied immediately to the connected device</li>
              <li>• Some parameters may have dependencies on the active test type</li>
              <li>• Read-only parameters show current device status</li>
              <li>• Use the SCPI Commands interface for advanced parameter control</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

function getDefaultParameterGroups(): ParameterGroup[] {
  return [
    {
      id: 'test',
      name: 'Test Configuration',
      description: 'Basic test setup and configuration',
      parameters: [
        {
          id: 'active_test',
          name: 'Active Test',
          command: 'TEST:ACTIVE',
          type: 'select',
          value: 'CWG',
          options: [
            { value: 'CWG', label: 'Continuous Wave Generator' },
            { value: 'EFT', label: 'Electrical Fast Transient' },
            { value: 'DIPS_AC', label: 'AC Voltage Dips' },
            { value: 'DIPS_DC', label: 'DC Voltage Dips' },
            { value: 'RINGWAVE', label: 'Ring Wave' },
          ],
          description: 'Currently active test type',
        },
        {
          id: 'cwg_level',
          name: 'CWG Level',
          command: 'TEST:CWG:LEVEL:VAL',
          type: 'number',
          value: 1000,
          unit: 'V',
          min: 0,
          max: 5000,
          description: 'Voltage level for CWG test',
        },
        {
          id: 'stop_on_fail',
          name: 'Stop on EUT Failure',
          command: 'TEST:SETTINGS:STOP_ON_FAIL',
          type: 'boolean',
          value: true,
          description: 'Stop test execution when EUT failure is detected',
        },
      ],
    },
    {
      id: 'power',
      name: 'Power Management',
      description: 'Power supply and measurement settings',
      parameters: [
        {
          id: 'power_setting',
          name: 'Power Output',
          command: 'PWR:SET',
          type: 'select',
          value: 'PWR1',
          options: [
            { value: 'OFF', label: 'Off' },
            { value: 'PWR1', label: 'Power 1' },
            { value: 'PWR2', label: 'Power 2' },
          ],
          description: 'Power relay setting for EUT port',
        },
        {
          id: 'voltage_measurement',
          name: 'Voltage Measurement',
          command: 'PWR:MEAS:1:VOLTAGE?',
          type: 'number',
          value: 230.5,
          unit: 'V',
          readonly: true,
          description: 'Current voltage measurement at point 1',
        },
        {
          id: 'current_measurement',
          name: 'Current Measurement',
          command: 'PWR:MEAS:1:CURRENT?',
          type: 'number',
          value: 0.52,
          unit: 'A',
          readonly: true,
          description: 'Current measurement at point 1',
        },
        {
          id: 'safety_circuit',
          name: 'Safety Circuit Status',
          command: 'PWR:SAFETY_CIRCUIT?',
          type: 'string',
          value: 'OK',
          readonly: true,
          description: 'Current state of the safety circuit',
        },
      ],
    },
    {
      id: 'environment',
      name: 'Environment',
      description: 'Environmental monitoring and settings',
      parameters: [
        {
          id: 'temperature',
          name: 'Temperature',
          command: 'ENVIRONMENT:TEMPERATURE?',
          type: 'number',
          value: 25.3,
          unit: '°C',
          readonly: true,
          description: 'Current ambient temperature',
        },
        {
          id: 'humidity',
          name: 'Humidity',
          command: 'ENVIRONMENT:HUMIDITY?',
          type: 'number',
          value: 48.2,
          unit: '%',
          readonly: true,
          description: 'Current relative humidity',
        },
        {
          id: 'temp_unit',
          name: 'Temperature Unit',
          command: 'ENVIRONMENT:UNIT_TEMP',
          type: 'select',
          value: 'C',
          options: [
            { value: 'C', label: 'Celsius' },
            { value: 'F', label: 'Fahrenheit' },
            { value: 'K', label: 'Kelvin' },
          ],
          description: 'Unit for temperature display',
        },
      ],
    },
    {
      id: 'protocol',
      name: 'Protocol Settings',
      description: 'Test protocol and reporting configuration',
      parameters: [
        {
          id: 'company',
          name: 'Company Name',
          command: 'PROTOCOL:GENERAL:COMPANY',
          type: 'string',
          value: 'Schweitzer Engineering Laboratories',
          description: 'Company name for protocol header',
        },
        {
          id: 'operator',
          name: 'Operator Name',
          command: 'PROTOCOL:GENERAL:OPERATOR',
          type: 'string',
          value: 'David Gonzalez',
          description: 'Operator name for protocol header',
        },
        {
          id: 'eut_description',
          name: 'EUT Description',
          command: 'PROTOCOL:EUT:DESCRIPTION',
          type: 'string',
          value: '',
          description: 'Description of the Equipment Under Test',
        },
        {
          id: 'create_protocol',
          name: 'Create Protocol',
          command: 'PROTOCOL:CREATE',
          type: 'boolean',
          value: true,
          description: 'Enable automatic protocol creation',
        },
        {
          id: 'csv_format',
          name: 'CSV Format',
          command: 'PROTOCOL:FORMAT:CSV',
          type: 'boolean',
          value: true,
          description: 'Create protocols in CSV format',
        },
        {
          id: 'html_format',
          name: 'HTML Format',
          command: 'PROTOCOL:FORMAT:HTML',
          type: 'boolean',
          value: false,
          description: 'Create protocols in HTML format',
        },
      ],
    },
    {
      id: 'audio',
      name: 'Audio Settings',
      description: 'Sound notifications and alerts',
      parameters: [
        {
          id: 'ready_trigger_sound',
          name: 'Ready for Trigger Sound',
          command: 'AUDIO:SOUNDS:READY_TRIGGER',
          type: 'select',
          value: 'BEEP',
          options: [
            { value: 'NO', label: 'No Sound' },
            { value: 'BEEP', label: 'Beep' },
            { value: 'HIGH_PITCH', label: 'High Pitch' },
            { value: 'DOUBLE_BEEP', label: 'Double Beep' },
          ],
          description: 'Sound played when ready for trigger',
        },
        {
          id: 'trigger_sound',
          name: 'Trigger Sound',
          command: 'AUDIO:SOUNDS:TRIGGER',
          type: 'select',
          value: 'HIGH_PITCH',
          options: [
            { value: 'NO', label: 'No Sound' },
            { value: 'BEEP', label: 'Beep' },
            { value: 'HIGH_PITCH', label: 'High Pitch' },
            { value: 'DOUBLE_BEEP', label: 'Double Beep' },
          ],
          description: 'Sound played on trigger event',
        },
      ],
    },
  ];
}