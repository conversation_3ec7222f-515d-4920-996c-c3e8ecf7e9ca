/**
 * ConsoleUX.ts - Console user experience utilities
 * Equivalent to console_ux.py in the original project
 * Class and functions related to the console user experience
 */

export interface ConsoleTheme {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

export interface ProgressBarOptions {
  width: number;
  showPercentage: boolean;
  showTime: boolean;
  customFormat?: string;
}

export class ConsoleUX {
  private static readonly DEFAULT_THEME: ConsoleTheme = {
    primary: '#2563eb',
    secondary: '#64748b',
    success: '#16a34a',
    warning: '#d97706',
    error: '#dc2626',
    info: '#0891b2',
  };

  private theme: ConsoleTheme;

  constructor(theme?: Partial<ConsoleTheme>) {
    this.theme = { ...ConsoleUX.DEFAULT_THEME, ...theme };
  }

  /**
   * Display welcome banner
   */
  displayWelcomeBanner(): void {
    const banner = `
╔══════════════════════════════════════════════════════════════╗
║                    PyEMC Control Software                    ║
║              EMC Partner Generator Control                   ║
║                                                              ║
║  A comprehensive solution for commanding and controlling     ║
║  EMC Partner generators through web interface               ║
╚══════════════════════════════════════════════════════════════╝
    `;
    
    console.log(banner);
  }

  /**
   * Display device connection status
   */
  displayConnectionStatus(
    deviceName: string,
    ipAddress: string,
    port: number,
    status: 'connected' | 'disconnected' | 'error'
  ): void {
    const statusSymbol = {
      connected: '✅',
      disconnected: '❌',
      error: '⚠️',
    }[status];

    const statusColor = {
      connected: this.theme.success,
      disconnected: this.theme.error,
      error: this.theme.warning,
    }[status];

    console.log(`${statusSymbol} Device: ${deviceName} (${ipAddress}:${port}) - ${status.toUpperCase()}`);
  }

  /**
   * Display test execution progress
   */
  displayTestProgress(
    testName: string,
    progress: number,
    status: string,
    options: Partial<ProgressBarOptions> = {}
  ): void {
    const opts: ProgressBarOptions = {
      width: 40,
      showPercentage: true,
      showTime: false,
      ...options,
    };

    const filled = Math.floor((progress / 100) * opts.width);
    const empty = opts.width - filled;
    
    const progressBar = '█'.repeat(filled) + '░'.repeat(empty);
    const percentage = opts.showPercentage ? ` ${progress.toFixed(1)}%` : '';
    
    console.log(`🧪 ${testName}: [${progressBar}]${percentage} - ${status}`);
  }

  /**
   * Display parameter table
   */
  displayParameterTable(parameters: Record<string, any>): void {
    console.log('\n📋 Test Parameters:');
    console.log('┌─────────────────────┬─────────────────────┬──────────┐');
    console.log('│ Parameter           │ Value               │ Unit     │');
    console.log('├─────────────────────┼─────────────────────┼──────────┤');

    Object.entries(parameters).forEach(([key, value]) => {
      const paramName = key.padEnd(19);
      const paramValue = String(value).padEnd(19);
      const unit = this.getParameterUnit(key).padEnd(8);
      console.log(`│ ${paramName} │ ${paramValue} │ ${unit} │`);
    });

    console.log('└─────────────────────┴─────────────────────┴──────────┘\n');
  }

  /**
   * Display test results summary
   */
  displayTestResults(results: {
    testName: string;
    status: 'success' | 'failed' | 'stopped';
    duration: number;
    parameters: Record<string, any>;
    errorMessage?: string;
  }): void {
    const statusSymbol = {
      success: '✅',
      failed: '❌',
      stopped: '⏹️',
    }[results.status];

    const durationFormatted = this.formatDuration(results.duration);

    console.log('\n' + '='.repeat(60));
    console.log(`${statusSymbol} Test Results: ${results.testName}`);
    console.log('='.repeat(60));
    console.log(`Status: ${results.status.toUpperCase()}`);
    console.log(`Duration: ${durationFormatted}`);
    
    if (results.errorMessage) {
      console.log(`Error: ${results.errorMessage}`);
    }

    this.displayParameterTable(results.parameters);
  }

  /**
   * Display error message with formatting
   */
  displayError(message: string, details?: string): void {
    console.log(`❌ Error: ${message}`);
    if (details) {
      console.log(`   Details: ${details}`);
    }
  }

  /**
   * Display warning message
   */
  displayWarning(message: string): void {
    console.log(`⚠️  Warning: ${message}`);
  }

  /**
   * Display info message
   */
  displayInfo(message: string): void {
    console.log(`ℹ️  Info: ${message}`);
  }

  /**
   * Display success message
   */
  displaySuccess(message: string): void {
    console.log(`✅ Success: ${message}`);
  }

  /**
   * Display command help
   */
  displayCommandHelp(commands: Array<{
    command: string;
    description: string;
    example?: string;
  }>): void {
    console.log('\n📖 Available Commands:');
    console.log('┌─────────────────────────┬─────────────────────────────────────────┐');
    console.log('│ Command                 │ Description                             │');
    console.log('├─────────────────────────┼─────────────────────────────────────────┤');

    commands.forEach(({ command, description }) => {
      const cmd = command.padEnd(23);
      const desc = description.padEnd(39);
      console.log(`│ ${cmd} │ ${desc} │`);
    });

    console.log('└─────────────────────────┴─────────────────────────────────────────┘\n');
  }

  /**
   * Display device status dashboard
   */
  displayDeviceStatus(status: {
    temperature: number;
    voltage: number;
    current: number;
    power: number;
    isReady: boolean;
    errorCount: number;
  }): void {
    console.log('\n🖥️  Device Status Dashboard');
    console.log('┌─────────────────┬─────────────┬────────┐');
    console.log('│ Parameter       │ Value       │ Status │');
    console.log('├─────────────────┼─────────────┼────────┤');

    const statusIcon = status.isReady ? '🟢' : '🔴';
    const tempStatus = status.temperature < 40 ? '🟢' : '🟡';
    const errorStatus = status.errorCount === 0 ? '🟢' : '🔴';

    console.log(`│ Ready           │ ${status.isReady ? 'Yes' : 'No'.padEnd(11)} │ ${statusIcon}      │`);
    console.log(`│ Temperature     │ ${status.temperature.toFixed(1)}°C      │ ${tempStatus}      │`);
    console.log(`│ Voltage         │ ${status.voltage.toFixed(1)}V       │ 🟢     │`);
    console.log(`│ Current         │ ${status.current.toFixed(2)}A        │ 🟢     │`);
    console.log(`│ Power           │ ${status.power.toFixed(1)}W       │ 🟢     │`);
    console.log(`│ Error Count     │ ${status.errorCount.toString().padEnd(11)} │ ${errorStatus}      │`);
    console.log('└─────────────────┴─────────────┴────────┘\n');
  }

  /**
   * Create interactive menu
   */
  createMenu(title: string, options: Array<{ key: string; label: string; description?: string }>): void {
    console.log(`\n📋 ${title}`);
    console.log('─'.repeat(title.length + 4));

    options.forEach(({ key, label, description }) => {
      const desc = description ? ` - ${description}` : '';
      console.log(`${key}. ${label}${desc}`);
    });

    console.log('\nEnter your choice:');
  }

  /**
   * Format duration in milliseconds to human readable format
   */
  private formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Get parameter unit for display
   */
  private getParameterUnit(parameter: string): string {
    const units: Record<string, string> = {
      level: 'V',
      voltage: 'V',
      current: 'A',
      power: 'W',
      frequency: 'Hz',
      temperature: '°C',
      humidity: '%',
      duration: 's',
      numberOfPulses: 'pulses',
      impedance: 'Ω',
    };

    return units[parameter] || '';
  }

  /**
   * Clear console screen
   */
  clearScreen(): void {
    console.clear();
  }

  /**
   * Display separator line
   */
  displaySeparator(char: string = '─', length: number = 60): void {
    console.log(char.repeat(length));
  }
}