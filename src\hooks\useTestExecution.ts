import { useState, useCallback } from 'react';
import { TestResult, TestParameters } from '../types';

export function useTestExecution() {
  const [currentTest, setCurrentTest] = useState<TestResult | null>(null);
  const [testHistory, setTestHistory] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const startTest = useCallback(async (profileName: string, parameters: TestParameters) => {
    const testId = Date.now().toString();
    const startTime = Date.now();

    const newTest: TestResult = {
      id: testId,
      timestamp: new Date(),
      profileName,
      parameters,
      status: 'running',
      duration: 0,
    };

    setCurrentTest(newTest);
    setIsRunning(true);

    try {
      // Simulate test execution
      const testDuration = Math.random() * 10000 + 5000; // 5-15 seconds
      
      await new Promise((resolve, reject) => {
        const interval = setInterval(() => {
          const elapsed = Date.now() - startTime;
          
          setCurrentTest(prev => prev ? {
            ...prev,
            duration: elapsed,
          } : null);

          if (elapsed >= testDuration) {
            clearInterval(interval);
            
            // Simulate random success/failure
            if (Math.random() > 0.1) {
              resolve(undefined);
            } else {
              reject(new Error('Test failed due to device timeout'));
            }
          }
        }, 100);
      });

      const completedTest: TestResult = {
        ...newTest,
        status: 'success',
        duration: Date.now() - startTime,
      };

      setCurrentTest(completedTest);
      setTestHistory(prev => [completedTest, ...prev]);
      
      return completedTest;
    } catch (error) {
      const failedTest: TestResult = {
        ...newTest,
        status: 'failed',
        duration: Date.now() - startTime,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      };

      setCurrentTest(failedTest);
      setTestHistory(prev => [failedTest, ...prev]);
      
      throw error;
    } finally {
      setIsRunning(false);
    }
  }, []);

  const stopTest = useCallback(() => {
    if (currentTest && isRunning) {
      const stoppedTest: TestResult = {
        ...currentTest,
        status: 'stopped',
      };

      setCurrentTest(stoppedTest);
      setTestHistory(prev => [stoppedTest, ...prev]);
      setIsRunning(false);
    }
  }, [currentTest, isRunning]);

  const clearHistory = useCallback(() => {
    setTestHistory([]);
  }, []);

  return {
    currentTest,
    testHistory,
    isRunning,
    startTest,
    stopTest,
    clearHistory,
  };
}