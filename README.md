# PyEMC Control Software

A Python-based command-line interface (CLI) for controlling EMC Partner generators, designed to reduce the need for front panel HMI manipulation and streamline electromagnetic compatibility testing workflows.

## 🎯 Project Overview

The PyEMC Control Software provides a comprehensive solution for commanding and controlling various EMC Partner generator models through a CLI interface. This software is specifically designed for use by technicians and engineers at SEL (Schweitzer Engineering Laboratories) to enhance testing efficiency and consistency.

### Supported Generator Models

- [ ] IMU-4000
- [ ] IMU-3000  
- [ ] IMU-MG1
- [ ] DOW-3000-S-F
- [ ] DOW-CG1

## ✨ Key Features

### For Basic Users (Technicians)

- **Predefined Test Profiles**: Ready-to-use test configurations for standard testing scenarios
- **Simple Parameter Querying**: Easy access to current device settings and test parameters
- **Guided User Interface**: Intuitive menu-driven CLI with clear instructions
- **Help System**: Built-in documentation and usage instructions

### For Advanced Users (Engineers)

- **Custom Command Strings**: Direct command execution based on device capabilities
- **Interactive Parameter Modification**: Real-time adjustment of test parameters
- **Parametric Testing**: Automated tests with varying parameters
- **Custom Test Profiles**: Creation and execution of specialized test configurations

### System Capabilities

- **Device Communication**: Robust TCP/IP and serial communication with generators
- **Error Handling**: Comprehensive logging and error reporting system
- **Test Management**: Complete test lifecycle management from setup to execution
- **Protocol Support**: CSV and HTML report generation capabilities

## 🏗️ Architecture

The software follows a modular architecture with clear separation of concerns:

```
PyEMC Control Software/
├── EMC PyControl/              # Main application directory
│   ├── PyCMI.py               # Main command interface and entry point
│   ├── PySURGE.py             # Core device communication and test management
│   ├── PyDeviceProperties.py  # Device property management and parameter handling
│   ├── device_setup.py        # Device server setup and client handling
│   ├── client.py              # Client communication interface
│   ├── device_emulator.py     # Device emulation for testing
│   ├── test_profiles.py       # Predefined test configurations
│   └── requirements.txt       # Python dependencies
├── SDD.md                     # Software Design Document
└── README.md                  # This file
```

### Core Components

- **PyCMI.py**: Main command interface providing user interaction and test control
- **PySURGE.py**: Core module containing device connection classes and test management
- **PyDeviceProperties.py**: Manages device-specific properties and parameter mappings
- **TestManager**: Handles test execution, parameter management, and user interactions

## 🚀 Getting Started

### Prerequisites

- Python 3.8.10 or higher
- Network access to EMC Partner generators
- Required Python packages (see requirements.txt)

### Installation

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd "PyEMC Control Software"
   ```

2. **Set up virtual environment** (recommended):

   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**:

   ```bash
   cd "EMC PyControl"
   pip install -r requirements.txt
   ```

### Configuration

1. **Update device connection settings** in `PyCMI.py`:

   ```python
   DEVICE = "************"  # Your generator's IP address
   PORT = 50500             # Communication port
   ```

2. **Verify network connectivity** to your EMC Partner generator

### Basic Usage

1. **Start the application**:

   ```bash
   python PyCMI.py
   ```

2. **Follow the interactive prompts** to:
   - Connect to your generator
   - View current device settings
   - Select or modify test parameters
   - Execute tests

## 📋 Usage Examples

### Quick Test Execution

```python
# Connect to device and run a basic test
surge_gen = IMU3000_device_connection("************", 50500)
if surge_gen.connect():
    # View current settings
    current_settings = surge_gen.learn()
    
    # Start a test with current parameters
    surge_gen.start_test()
```

### Custom Parameter Configuration

```python
# Create test configuration
test_config = Surge_Test(
    coupling_device="INTERN",
    level=1000,
    polarity="POSITIVE",
    impedance="50",
    number_of_pulses=5
)

# Execute test with custom parameters
test_manager = TestManager(test_config, surge_gen)
test_manager.set_test_values(interactive=True)
```

## 🔧 Development Phases

### Phase 1 (Current)

- ✅ Basic CLI interface for generator control
- ✅ Device communication via TCP/IP
- ✅ Predefined test profiles
- ✅ Parameter modification capabilities
- ✅ Error handling and logging

### Phase 2 (Planned)

- 🔄 Integration with waveform capture software
- 🔄 Advanced analytics and reporting
- 🔄 Enhanced user interface improvements
- 🔄 Extended device model support

## 🛠️ Technical Details

### Communication Protocol

- **Primary**: TCP/IP socket communication (Port 50500)
- **Backup**: Serial communication support
- **Commands**: SCPI-like command structure for device control

### Error Handling

- Centralized error logging to `device_communication.log`
- User-friendly error messages
- Automatic retry mechanisms for transient errors
- Comprehensive exception handling

### Testing Framework

- Built-in device emulator for development testing
- Parametric test capabilities
- Test result logging and reporting
- Protocol management (CSV/HTML output)

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](EMC%20PyControl/LICENSE) file for details.

**Copyright (c) 2024 David Gonzalez**

## 🤝 Contributing

This software is designed for internal use at SEL. For questions, improvements, or bug reports, please contact the development team.

## 📚 Documentation

- **[Software Design Document](SDD.md)**: Comprehensive technical documentation
- **[EPOS Programmer Manual](EPOS_Programmer_Manual.pdf)**: Device programming reference

## 🔍 Troubleshooting

### Common Issues

1. **Connection Failed**: Verify IP address and network connectivity
2. **Invalid Parameters**: Check device capabilities and parameter ranges
3. **Test Execution Errors**: Review device status and error logs

### Support

For technical support and questions:

- Review the Software Design Document (SDD.md)
- Check error logs in `device_communication.log`
- Verify device network configuration
- Ensure all dependencies are properly installed

---

*This software is designed to enhance EMC testing efficiency and consistency while providing both basic and advanced functionality for different user types.*
