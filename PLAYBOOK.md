# PyEMC Control Software - Complete Playbook

## 📚 Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture Deep Dive](#architecture-deep-dive)
3. [File-by-File Guide](#file-by-file-guide)
4. [Development Setup](#development-setup)
5. [Adding New Features](#adding-new-features)
6. [Customization Guide](#customization-guide)
7. [Debugging Guide](#debugging-guide)
8. [Deployment Instructions](#deployment-instructions)
9. [4-Week Learning Path](#4-week-learning-path)
10. [Help Resources](#help-resources)

---

## 🎯 Project Overview

The PyEMC Control Software is a modern web-based interface for controlling EMC Partner generators, designed to streamline electromagnetic compatibility testing workflows. This application replaces traditional command-line interfaces with an intuitive React-based web UI.

### Key Technologies

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS + Custom Design System
- **Icons**: Lucide React
- **State Management**: React Hooks + Custom Hooks
- **Communication**: Simulated TCP/IP (SCPI commands)

### Supported Generator Models

- IMU-4000, IMU-3000, IMU-MG1
- DOW-3000-S-F, DOW-CG1

---

## 🏗️ Architecture Deep Dive

### Application Structure

```ts
src/
├── components/          # React UI components
│   ├── layout/         # Header, Sidebar, layout components
│   ├── connection/     # Device connection management
│   ├── monitoring/     # Real-time device monitoring
│   ├── profiles/       # Test profile management
│   ├── execution/      # Test execution controls
│   ├── history/        # Test history and results
│   ├── parameters/     # Custom parameter configuration
│   ├── commands/       # SCPI command interface
│   ├── interface/      # Main CMI (Command Interface)
│   └── ui/            # Reusable UI components
├── core/               # Business logic and device communication
├── hooks/              # Custom React hooks for state management
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

### Core Classes and Their Responsibilities

#### GeneratorBase (`src/core/GeneratorBase.ts`)

- **Purpose**: Core device interface and communication
- **Key Methods**: `connect()`, `disconnect()`, `sendCommand()`, `queryDeviceInfo()`
- **Simulates**: TCP/IP communication with EMC generators

#### TestManager (`src/core/TestManager.ts`)

- **Purpose**: Test execution and parameter management
- **Key Methods**: `executeTest()`, `stopTest()`, `setTestParameters()`
- **Handles**: Test lifecycle, parameter validation, result collection

#### PowerUser (`src/core/PowerUser.ts`)

- **Purpose**: Advanced functionality for engineers
- **Key Methods**: `executeCommandSequence()`, `runParametricTest()`, `runDiagnostics()`
- **Features**: Custom command sequences, parametric testing, advanced diagnostics

#### ErrorHandler (`src/core/ErrorHandling.ts`)

- **Purpose**: Centralized error handling and logging
- **Key Methods**: `logError()`, `logConnectionError()`, `logValidationError()`
- **Features**: Error categorization, severity levels, resolution tracking

#### ConsoleUX (`src/core/ConsoleUX.ts`)

- **Purpose**: Console user experience utilities
- **Key Methods**: `displayWelcomeBanner()`, `displayTestResults()`, `createProgressBar()`
- **Features**: Formatted output, progress tracking, themed console display

---

## 📁 File-by-File Guide

### Entry Points

- **`index.html`**: Main HTML template with Vite integration
- **`src/main.tsx`**: React application entry point
- **`src/App.tsx`**: Main application component with routing logic

### Core Components

#### Layout Components

- **`src/components/layout/Header.tsx`**: Top navigation bar with user info and settings
- **`src/components/layout/Sidebar.tsx`**: Navigation sidebar with role-based menu items

#### Device Management

- **`src/components/connection/DeviceConnection.tsx`**: Device connection form and management
- **`src/components/monitoring/DeviceMonitor.tsx`**: Real-time device status monitoring

#### Test Management

- **`src/components/profiles/TestProfiles.tsx`**: Predefined and custom test profiles
- **`src/components/execution/TestExecution.tsx`**: Test execution controls and monitoring
- **`src/components/history/TestHistory.tsx`**: Historical test results and analysis

#### Advanced Features

- **`src/components/parameters/CustomParameters.tsx`**: Advanced parameter configuration
- **`src/components/commands/SCPICommands.tsx`**: Direct SCPI command interface
- **`src/components/interface/CMI.tsx`**: Main command interface (equivalent to PyCMI.py)

### UI Components

- **`src/components/ui/Button.tsx`**: Customizable button component with variants
- **`src/components/ui/Card.tsx`**: Card container component
- **`src/components/ui/Input.tsx`**: Form input component
- **`src/components/ui/Select.tsx`**: Dropdown selection component
- **`src/components/ui/Badge.tsx`**: Status and label badges

### State Management

- **`src/hooks/useDeviceConnection.ts`**: Device connection state and operations
- **`src/hooks/useTestProfiles.ts`**: Test profile management
- **`src/hooks/useTestExecution.ts`**: Test execution state

### Configuration

- **`tailwind.config.js`**: Tailwind CSS configuration with custom theme
- **`package.json`**: Project dependencies and scripts
- **`postcss.config.js`**: PostCSS configuration for Tailwind

---

## 🚀 Development Setup

### Prerequisites

- Node.js 16+ and npm/yarn
- Modern web browser
- Code editor (VS Code recommended)

### Installation Steps

```bash
# Clone the repository
git clone <repository-url>
cd Bolt_PyEMC_Controller

# Install dependencies
npm install

# Start development server
npm run dev

# Open browser to http://localhost:5173
```

### Development Scripts

```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run preview  # Preview production build
npm run lint     # Run ESLint
```

---

## ✨ Adding New Features

### 1. Adding a New Generator Model

#### Step 1: Update Type Definitions

```typescript
// src/types/index.ts
export type GeneratorModel = 'IMU-4000' | 'IMU-3000' | 'IMU-MG1' | 'DOW-3000-S-F' | 'DOW-CG1' | 'NEW-MODEL';
```

#### Step 2: Add Model-Specific Properties

```typescript
// src/core/NewModelProperties.ts
export interface NewModelParameters {
  // Define model-specific parameters
}

export class NewModelProperties {
  static getLimits(): NewModelLimits {
    // Return parameter limits
  }
  
  static validateParameters(params: NewModelParameters): boolean {
    // Validate parameters
  }
}
```

#### Step 3: Update GeneratorBase

```typescript
// src/core/GeneratorBase.ts
private simulateResponse(command: string): string {
  // Add new model responses
  if (this.deviceInfo?.model === 'NEW-MODEL') {
    // Handle new model commands
  }
}
```

### 2. Adding a New Test Type

#### Step 1: Define Test Parameters

```typescript
// src/types/index.ts
export interface NewTestParameters extends TestParameters {
  newParameter1: number;
  newParameter2: string;
}
```

#### Step 2: Create Test Component

```typescript
// src/components/tests/NewTestType.tsx
export function NewTestType() {
  // Component implementation
}
```

#### Step 3: Update Test Manager

```typescript
// src/core/TestManager.ts
async executeNewTest(params: NewTestParameters): Promise<TestResult> {
  // Implementation
}
```

### 3. Adding Custom UI Components

#### Step 1: Create Component

```typescript
// src/components/ui/NewComponent.tsx
interface NewComponentProps {
  // Define props
}

export function NewComponent({ ...props }: NewComponentProps) {
  return (
    <div className="custom-component">
      {/* Component JSX */}
    </div>
  );
}
```

#### Step 2: Add Styling (if needed)

```css
/* src/index.css */
@layer components {
  .custom-component {
    @apply bg-white border rounded-lg p-4;
  }
}
```

---

## 🎨 Customization Guide

### Color Scheme Customization

#### Updating Brand Colors

```javascript
// tailwind.config.js
theme: {
  extend: {
    colors: {
      primary: {
        50: '#your-color-50',
        // ... define all shades
        900: '#your-color-900',
      }
    }
  }
}
```

#### CSS Custom Properties

```css
/* src/index.css */
:root {
  --primary-color: #2563eb;
  --secondary-color: #64748b;
}
```

### Typography Customization

```javascript
// tailwind.config.js
fontFamily: {
  sans: ['Your-Font', 'system-ui', 'sans-serif'],
  mono: ['Your-Mono-Font', 'Consolas', 'monospace'],
}
```

### Layout Customization

#### Sidebar Width

```typescript
// src/components/layout/Sidebar.tsx
<aside className="w-64 bg-white border-r"> {/* Change w-64 to desired width */}
```

#### Header Height

```typescript
// src/components/layout/Header.tsx
<header className="h-16 bg-white border-b"> {/* Adjust h-16 */}
```

### Component Styling

#### Button Variants

```typescript
// src/components/ui/Button.tsx
const variants = {
  primary: 'bg-primary-600 hover:bg-primary-700 text-white',
  custom: 'bg-custom-600 hover:bg-custom-700 text-white', // Add custom variant
}
```

---

## 🐛 Debugging Guide

### Common Issues and Solutions

#### 1. Device Connection Failures

**Symptoms**: Connection timeouts, "Device not connected" errors
**Solutions**:

- Check IP address and port configuration
- Verify network connectivity
- Review `GeneratorBase.connect()` method
- Check browser console for WebSocket/fetch errors

#### 2. Test Execution Errors

**Symptoms**: Tests fail to start, parameter validation errors
**Solutions**:

- Validate test parameters in `TestManager.setTestParameters()`
- Check SCPI command format in `GeneratorBase.sendCommand()`
- Review error logs in `ErrorHandler`

#### 3. UI Rendering Issues

**Symptoms**: Components not displaying correctly, styling problems
**Solutions**:

- Check Tailwind CSS classes
- Verify component props and state
- Use React Developer Tools
- Check browser console for JavaScript errors

#### 4. State Management Issues

**Symptoms**: Data not updating, stale state
**Solutions**:

- Review custom hooks (`useDeviceConnection`, `useTestProfiles`)
- Check useEffect dependencies
- Verify state updates are immutable

### Debugging Tools

#### Browser Developer Tools

```javascript
// Add to components for debugging
console.log('Component state:', state);
console.log('Props:', props);
```

#### React Developer Tools

- Install React DevTools browser extension
- Inspect component tree and state
- Profile performance issues

#### Network Debugging

```javascript
// Monitor simulated device communication
// src/core/GeneratorBase.ts
async sendCommand(command: string): Promise<string> {
  console.log('Sending command:', command);
  const response = this.simulateResponse(command);
  console.log('Response:', response);
  return response;
}
```

### Error Logging

```typescript
// Use ErrorHandler for centralized logging
const errorHandler = ErrorHandler.getInstance();
errorHandler.logError(ErrorSeverity.ERROR, 'COMPONENT', 'Error message', 'Details');
```

---

## 🚀 Deployment Instructions

### Production Build

```bash
# Create optimized production build
npm run build

# Preview production build locally
npm run preview
```

### Static Hosting (Netlify, Vercel, GitHub Pages)

```bash
# Build the project
npm run build

# Deploy the 'dist' folder to your hosting provider
```

### Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Environment Configuration

```bash
# .env.production
VITE_API_URL=https://your-api-url.com
VITE_DEVICE_DEFAULT_IP=************
VITE_DEVICE_DEFAULT_PORT=50500
```

---

## 📅 4-Week Learning Path

### Week 1: Foundation & Setup

**Goals**: Understand project structure and get development environment running

**Day 1-2**: Project Overview

- Read README.md and this PLAYBOOK.md
- Understand EMC testing concepts
- Set up development environment

**Day 3-4**: Architecture Understanding

- Study `src/App.tsx` and main component structure
- Understand React hooks and state management
- Explore `src/types/index.ts` for data models

**Day 5-7**: Core Classes Deep Dive

- Study `GeneratorBase.ts` - device communication
- Understand `TestManager.ts` - test execution
- Explore `ErrorHandler.ts` - error management

### Week 2: UI Components & Styling

**Goals**: Master the component system and styling approach

**Day 8-10**: Layout Components

- Study Header and Sidebar components
- Understand navigation and routing
- Practice modifying layout styles

**Day 11-12**: UI Component Library

- Explore `src/components/ui/` components
- Understand Button, Card, Input variants
- Practice creating custom components

**Day 13-14**: Tailwind CSS Mastery

- Study `tailwind.config.js` configuration
- Practice responsive design patterns
- Customize color schemes and typography

### Week 3: Device Management & Testing

**Goals**: Understand device communication and test execution

**Day 15-17**: Device Connection

- Study `DeviceConnection.tsx` component
- Understand `useDeviceConnection` hook
- Practice adding new device models

**Day 18-19**: Test Management

- Explore test profiles and execution
- Understand parameter validation
- Practice creating custom test types

**Day 20-21**: Advanced Features

- Study PowerUser class for advanced functionality
- Understand SCPI command interface
- Practice parametric testing features

### Week 4: Advanced Topics & Deployment

**Goals**: Master advanced features and deployment

**Day 22-24**: Error Handling & Debugging

- Study ErrorHandler implementation
- Practice debugging techniques
- Implement custom error scenarios

**Day 25-26**: Performance & Optimization

- Understand React performance patterns
- Practice code splitting and lazy loading
- Optimize bundle size

**Day 27-28**: Deployment & Production

- Practice production builds
- Deploy to hosting platforms
- Set up CI/CD pipelines

---

## 🆘 Help Resources

### Documentation

- **React**: <https://react.dev/>
- **TypeScript**: <https://www.typescriptlang.org/docs/>
- **Tailwind CSS**: <https://tailwindcss.com/docs>
- **Vite**: <https://vitejs.dev/guide/>

### Community Resources

- **React Community**: <https://react.dev/community>
- **Stack Overflow**: Tag questions with `react`, `typescript`, `tailwindcss`
- **GitHub Discussions**: Use repository discussions for project-specific questions

### How to Ask Good Questions

#### Before Asking

1. **Search existing issues** and documentation
2. **Reproduce the problem** with minimal code
3. **Check browser console** for error messages
4. **Review recent changes** that might have caused the issue

#### When Asking

1. **Provide context**: What were you trying to achieve?
2. **Include error messages**: Copy exact error text
3. **Share code snippets**: Use relevant code examples
4. **Describe environment**: Browser, Node.js version, OS
5. **Show what you tried**: List attempted solutions

#### Example Good Question

```bash
Title: Device connection fails with timeout error

I'm trying to connect to an IMU-3000 generator but getting a timeout error.

Environment:
- Browser: Chrome 120
- Node.js: 18.17.0
- Device IP: ************

Error message:
"Connection failed: Device not connected"

Code:
```typescript
const connection = await connect({
  name: 'Test Device',
  ipAddress: '************',
  port: 50500,
  model: 'IMU-3000'
});
```

What I tried:

1. Verified IP address is correct
2. Checked network connectivity with ping
3. Reviewed GeneratorBase.connect() method

Expected: Successful connection
Actual: Timeout after 2 seconds

```bash

### Internal Code References
- **Device Communication**: `src/core/GeneratorBase.ts`
- **Test Management**: `src/core/TestManager.ts`
- **Error Handling**: `src/core/ErrorHandling.ts`
- **UI Components**: `src/components/ui/`
- **State Management**: `src/hooks/`
- **Type Definitions**: `src/types/index.ts`

---

## 🔧 Advanced Development Patterns

### Custom Hook Patterns

#### Creating Device-Specific Hooks
```typescript
// src/hooks/useIMU3000.ts
export function useIMU3000() {
  const [deviceState, setDeviceState] = useState<IMU3000State>();

  const sendIMU3000Command = useCallback(async (command: string) => {
    // IMU-3000 specific command handling
  }, []);

  return { deviceState, sendIMU3000Command };
}
```

#### State Management Patterns

```typescript
// src/hooks/useTestState.ts
export function useTestState() {
  const [state, setState] = useState<TestState>({
    isRunning: false,
    currentTest: null,
    results: []
  });

  const updateTestState = useCallback((updates: Partial<TestState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  return { state, updateTestState };
}
```

### Error Boundary Implementation

```typescript
// src/components/ErrorBoundary.tsx
class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorHandler = ErrorHandler.getInstance();
    errorHandler.logError(
      ErrorSeverity.CRITICAL,
      'REACT_ERROR',
      error.message,
      errorInfo.componentStack
    );
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}
```

### Performance Optimization

#### Memoization Patterns

```typescript
// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return calculateComplexTestParameters(parameters);
}, [parameters]);

// Memoize callback functions
const handleTestStart = useCallback(async () => {
  await testManager.executeTest();
}, [testManager]);
```

#### Code Splitting

```typescript
// Lazy load components
const AdvancedDiagnostics = lazy(() => import('./AdvancedDiagnostics'));

// Use with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <AdvancedDiagnostics />
</Suspense>
```

---

## 🧪 Testing Strategies

### Unit Testing Setup

```bash
# Install testing dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom vitest
```

### Component Testing Examples

```typescript
// src/components/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../ui/Button';

describe('Button Component', () => {
  test('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  test('calls onClick handler', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Core Class Testing

```typescript
// src/core/__tests__/GeneratorBase.test.ts
import { GeneratorBase } from '../GeneratorBase';

describe('GeneratorBase', () => {
  let generator: GeneratorBase;

  beforeEach(() => {
    generator = new GeneratorBase('************', 50500);
  });

  test('connects successfully', async () => {
    const result = await generator.connect();
    expect(result).toBe(true);
    expect(generator.isDeviceConnected()).toBe(true);
  });

  test('sends commands correctly', async () => {
    await generator.connect();
    const response = await generator.sendCommand('*IDN?');
    expect(response).toContain('EMC Partner');
  });
});
```

### Integration Testing

```typescript
// src/__tests__/DeviceWorkflow.test.tsx
describe('Device Connection Workflow', () => {
  test('complete connection and test execution', async () => {
    render(<App />);

    // Navigate to connection page
    fireEvent.click(screen.getByText('Device Connection'));

    // Fill connection form
    fireEvent.change(screen.getByLabelText('IP Address'), {
      target: { value: '************' }
    });

    // Connect to device
    fireEvent.click(screen.getByText('Connect'));

    // Verify connection success
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument();
    });
  });
});
```

---

## 📊 Monitoring and Analytics

### Performance Monitoring

```typescript
// src/utils/performance.ts
export class PerformanceMonitor {
  static measureTestExecution(testName: string) {
    const startTime = performance.now();

    return {
      end: () => {
        const duration = performance.now() - startTime;
        console.log(`Test ${testName} took ${duration}ms`);
        return duration;
      }
    };
  }
}

// Usage in TestManager
const monitor = PerformanceMonitor.measureTestExecution(testName);
await this.executeTest();
const duration = monitor.end();
```

### Error Analytics

```typescript
// src/core/Analytics.ts
export class Analytics {
  static trackError(error: ErrorEntry) {
    // Send to analytics service
    console.log('Error tracked:', {
      category: error.category,
      severity: error.severity,
      timestamp: error.timestamp
    });
  }

  static trackTestExecution(result: TestResult) {
    // Track test metrics
    console.log('Test tracked:', {
      duration: result.duration,
      status: result.status,
      profileName: result.profileName
    });
  }
}
```

---

## 🔐 Security Considerations

### Input Validation

```typescript
// src/utils/validation.ts
export class InputValidator {
  static validateIPAddress(ip: string): boolean {
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipRegex.test(ip)) return false;

    return ip.split('.').every(octet => {
      const num = parseInt(octet);
      return num >= 0 && num <= 255;
    });
  }

  static validatePort(port: number): boolean {
    return port >= 1 && port <= 65535;
  }

  static sanitizeCommand(command: string): string {
    // Remove potentially dangerous characters
    return command.replace(/[;&|`$()]/g, '');
  }
}
```

### SCPI Command Sanitization

```typescript
// src/core/CommandSanitizer.ts
export class CommandSanitizer {
  private static readonly ALLOWED_COMMANDS = [
    '*IDN?', '*LRN?', 'TEST:ACTIVE', 'RUN:START', 'RUN:STOP'
  ];

  static validateCommand(command: string): boolean {
    const cmd = command.toUpperCase().trim();
    return this.ALLOWED_COMMANDS.some(allowed =>
      cmd.startsWith(allowed)
    );
  }

  static sanitizeParameters(params: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(params)) {
      if (typeof value === 'string') {
        sanitized[key] = value.replace(/[<>'"&]/g, '');
      } else if (typeof value === 'number') {
        sanitized[key] = Math.max(0, Math.min(value, 10000)); // Reasonable limits
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }
}
```

---

## 🌐 Internationalization (i18n)

### Setting Up i18n

```bash
npm install react-i18next i18next
```

```typescript
// src/i18n/config.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: {
          'device.connection.title': 'Device Connection',
          'test.execution.start': 'Start Test',
          'error.connection.failed': 'Connection failed'
        }
      },
      es: {
        translation: {
          'device.connection.title': 'Conexión del Dispositivo',
          'test.execution.start': 'Iniciar Prueba',
          'error.connection.failed': 'Conexión fallida'
        }
      }
    },
    lng: 'en',
    fallbackLng: 'en'
  });
```

### Using Translations

```typescript
// src/components/DeviceConnection.tsx
import { useTranslation } from 'react-i18next';

export function DeviceConnection() {
  const { t } = useTranslation();

  return (
    <Card>
      <CardHeader>
        <h2>{t('device.connection.title')}</h2>
      </CardHeader>
    </Card>
  );
}
```

---

## 🚀 Advanced Deployment Scenarios

### Kubernetes Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pyemc-control
spec:
  replicas: 3
  selector:
    matchLabels:
      app: pyemc-control
  template:
    metadata:
      labels:
        app: pyemc-control
    spec:
      containers:
      - name: pyemc-control
        image: pyemc-control:latest
        ports:
        - containerPort: 80
        env:
        - name: VITE_API_URL
          value: "https://api.example.com"
---
apiVersion: v1
kind: Service
metadata:
  name: pyemc-control-service
spec:
  selector:
    app: pyemc-control
  ports:
  - port: 80
    targetPort: 80
  type: LoadBalancer
```

### CI/CD Pipeline (GitHub Actions)

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Build application
      run: npm run build

    - name: Deploy to Netlify
      uses: nwtgck/actions-netlify@v2.0
      with:
        publish-dir: './dist'
        production-branch: main
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
```

---

## 💡 Practical Examples and Recipes

### Example 1: Creating a Custom Test Profile

```typescript
// Step 1: Define custom parameters
interface CustomSurgeTest extends TestParameters {
  riseTime: number;
  fallTime: number;
  burstCount: number;
}

// Step 2: Create the test profile
const customProfile: TestProfile = {
  id: 'custom-surge-001',
  name: 'High-Speed Surge Test',
  description: 'Custom surge test with fast rise/fall times',
  parameters: {
    couplingDevice: 'EXTERN',
    level: 2000,
    polarity: 'BOTH',
    impedance: '50',
    numberOfPulses: 10,
    riseTime: 1.2,
    fallTime: 50,
    burstCount: 3
  },
  isCustom: true
};

// Step 3: Add validation
function validateCustomSurgeTest(params: CustomSurgeTest): boolean {
  return params.riseTime >= 0.8 &&
         params.riseTime <= 1.2 &&
         params.fallTime >= 40 &&
         params.fallTime <= 60;
}
```

### Example 2: Implementing Real-time Data Streaming

```typescript
// src/hooks/useRealTimeData.ts
export function useRealTimeData(deviceId: string) {
  const [data, setData] = useState<RealTimeData[]>([]);

  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        const newData = await fetchDeviceData(deviceId);
        setData(prev => [...prev.slice(-99), newData]); // Keep last 100 points
      } catch (error) {
        console.error('Failed to fetch real-time data:', error);
      }
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [deviceId]);

  return data;
}

// Usage in component
function RealTimeChart() {
  const data = useRealTimeData('device-001');

  return (
    <div className="chart-container">
      {/* Render chart with real-time data */}
    </div>
  );
}
```

### Example 3: Batch Test Execution

```typescript
// src/core/BatchTestManager.ts
export class BatchTestManager {
  private testManager: TestManager;
  private results: TestResult[] = [];

  async executeBatchTests(profiles: TestProfile[]): Promise<TestResult[]> {
    this.results = [];

    for (const profile of profiles) {
      try {
        console.log(`Starting test: ${profile.name}`);

        // Set test configuration
        this.testManager.setTestConfiguration({
          name: profile.name,
          description: profile.description,
          parameters: profile.parameters
        });

        // Execute test
        const result = await this.testManager.executeTest();
        this.results.push(result);

        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.error(`Test failed: ${profile.name}`, error);
        this.results.push({
          id: Date.now().toString(),
          timestamp: new Date(),
          profileName: profile.name,
          parameters: profile.parameters,
          status: 'failed',
          duration: 0,
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return this.results;
  }
}
```

---

## 🔍 Troubleshooting Scenarios

### Scenario 1: Device Won't Connect

**Problem**: Device connection always fails with timeout

**Debugging Steps**:

1. Check network connectivity:

   ```bash
   ping ************
   telnet ************ 50500
   ```

2. Verify device configuration in code:

   ```typescript
   // Check GeneratorBase constructor
   const generator = new GeneratorBase('************', 50500);
   ```

3. Add debug logging:

   ```typescript
   // In GeneratorBase.connect()
   console.log('Attempting connection to:', this.ipAddress, this.port);
   ```

**Common Solutions**:

- Verify IP address and port
- Check firewall settings
- Ensure device is powered on and network-accessible
- Try different port numbers (50500, 5025, 23)

### Scenario 2: Tests Execute But No Results

**Problem**: Tests appear to run but don't produce expected results

**Debugging Steps**:

1. Check SCPI command responses:

   ```typescript
   // Add logging in sendCommand
   async sendCommand(command: string): Promise<string> {
     console.log('Command:', command);
     const response = this.simulateResponse(command);
     console.log('Response:', response);
     return response;
   }
   ```

2. Verify parameter validation:

   ```typescript
   // In TestManager.setTestParameters()
   console.log('Setting parameters:', parameters);
   ```

3. Check test execution flow:

   ```typescript
   // Add breakpoints or logs in executeTest()
   ```

**Common Solutions**:

- Verify SCPI command syntax
- Check parameter ranges and validation
- Ensure proper test sequence timing
- Validate device model compatibility

### Scenario 3: UI Components Not Updating

**Problem**: Device status or test results don't update in UI

**Debugging Steps**:

1. Check React DevTools for state changes
2. Verify useEffect dependencies:

   ```typescript
   useEffect(() => {
     // Effect code
   }, [dependency1, dependency2]); // Ensure all dependencies are listed
   ```

3. Check for state mutation:

   ```typescript
   // Wrong - mutating state
   state.results.push(newResult);

   // Correct - immutable update
   setState(prev => ({
     ...prev,
     results: [...prev.results, newResult]
   }));
   ```

**Common Solutions**:

- Add missing useEffect dependencies
- Ensure state updates are immutable
- Check for async state updates
- Verify component re-rendering conditions

---

## 📋 Quick Reference

### Essential Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run linting

# Testing
npm test             # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage

# Deployment
npm run build        # Build for production
npm run deploy       # Deploy to hosting (if configured)
```

### Key File Locations

```
Configuration:
├── package.json           # Dependencies and scripts
├── tailwind.config.js     # Styling configuration
├── postcss.config.js      # PostCSS configuration
└── index.html            # HTML template

Source Code:
├── src/App.tsx           # Main application component
├── src/main.tsx          # Application entry point
├── src/types/index.ts    # TypeScript definitions
├── src/core/             # Business logic
├── src/components/       # React components
├── src/hooks/            # Custom React hooks
└── src/utils/            # Utility functions
```

### Common Patterns

```typescript
// Custom Hook Pattern
export function useCustomHook() {
  const [state, setState] = useState(initialState);

  const action = useCallback(() => {
    // Action implementation
  }, [dependencies]);

  return { state, action };
}

// Component Pattern
export function Component({ prop1, prop2 }: Props) {
  const { state, action } = useCustomHook();

  return (
    <div className="component-class">
      {/* Component JSX */}
    </div>
  );
}

// Error Handling Pattern
try {
  const result = await riskyOperation();
  // Handle success
} catch (error) {
  const errorHandler = ErrorHandler.getInstance();
  errorHandler.logError(
    ErrorSeverity.ERROR,
    'OPERATION',
    error.message
  );
  // Handle error
}
```

### Useful Debugging Snippets

```typescript
// Log component renders
useEffect(() => {
  console.log('Component rendered with props:', props);
});

// Log state changes
useEffect(() => {
  console.log('State changed:', state);
}, [state]);

// Performance measurement
const start = performance.now();
// ... operation
console.log(`Operation took ${performance.now() - start}ms`);

// Network request debugging
fetch(url)
  .then(response => {
    console.log('Response status:', response.status);
    return response.json();
  })
  .then(data => console.log('Response data:', data))
  .catch(error => console.error('Request failed:', error));
```

---

## 🎓 Graduation Checklist

After completing the 4-week learning path, you should be able to:

### Week 1 Competencies ✅

- [ ] Understand the overall project architecture
- [ ] Set up and run the development environment
- [ ] Navigate the codebase confidently
- [ ] Explain the purpose of each core class
- [ ] Identify the main data flow patterns

### Week 2 Competencies ✅

- [ ] Create and modify UI components
- [ ] Customize styling with Tailwind CSS
- [ ] Understand the component hierarchy
- [ ] Implement responsive design patterns
- [ ] Use the design system effectively

### Week 3 Competencies ✅

- [ ] Manage device connections
- [ ] Create and execute test profiles
- [ ] Understand SCPI command structure
- [ ] Implement parameter validation
- [ ] Handle test execution workflows

### Week 4 Competencies ✅

- [ ] Debug common issues effectively
- [ ] Implement error handling patterns
- [ ] Optimize application performance
- [ ] Deploy to production environments
- [ ] Set up monitoring and analytics

### Master Level Goals 🎯

- [ ] Contribute new features independently
- [ ] Mentor other developers
- [ ] Design system architecture improvements
- [ ] Lead code reviews and technical discussions
- [ ] Implement complex testing scenarios

---

*This playbook is a living document. Update it as the project evolves and new features are added.*

**Last Updated**: 2025-07-01
**Version**: 1.0
**Contributors**: Development Team
