import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Terminal, User, Settings, HelpCircle, Zap } from 'lucide-react';
import { GeneratorBase } from '../../core/GeneratorBase';
import { TestManager } from '../../core/TestManager';
import { PowerUser } from '../../core/PowerUser';
import { ErrorHandler } from '../../core/ErrorHandling';
import { ConsoleUX } from '../../core/ConsoleUX';

interface CMIProps {
  userRole: 'technician' | 'engineer';
  onModeChange: (mode: 'basic' | 'advanced') => void;
}

export function CMI({ userRole, onModeChange }: CMIProps) {
  const [generator] = useState(() => new GeneratorBase('************', 50500));
  const [testManager] = useState(() => new TestManager(generator));
  const [powerUser] = useState(() => new PowerUser(generator, testManager));
  const [consoleUX] = useState(() => new ConsoleUX());
  const [errorHandler] = useState(() => ErrorHandler.getInstance());
  
  const [isConnected, setIsConnected] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [currentMode, setCurrentMode] = useState<'basic' | 'advanced'>('basic');

  useEffect(() => {
    // Initialize console UX
    consoleUX.displayWelcomeBanner();
  }, [consoleUX]);

  const handleConnect = async () => {
    try {
      const connected = await generator.connect();
      setIsConnected(connected);
      
      if (connected) {
        const info = generator.getDeviceInfo();
        setDeviceInfo(info);
        consoleUX.displayConnectionStatus(
          info?.model || 'Unknown',
          '************',
          50500,
          'connected'
        );
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Connection failed';
      errorHandler.logConnectionError('************', 50500, errorMsg);
      consoleUX.displayError('Connection failed', errorMsg);
    }
  };

  const handleModeChange = (mode: 'basic' | 'advanced') => {
    setCurrentMode(mode);
    onModeChange(mode);
    
    if (mode === 'advanced') {
      consoleUX.displayInfo('Advanced mode enabled - Full SCPI access available');
    } else {
      consoleUX.displayInfo('Basic mode enabled - Guided interface active');
    }
  };

  const runDiagnostics = async () => {
    if (!isConnected) {
      consoleUX.displayWarning('Device not connected');
      return;
    }

    try {
      const diagnostics = await powerUser.runDiagnostics();
      console.log('Device Diagnostics:', diagnostics);
      consoleUX.displaySuccess('Diagnostics completed successfully');
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Diagnostics failed';
      consoleUX.displayError('Diagnostics failed', errorMsg);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-12 h-12 bg-primary-600 rounded-lg">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">Command Interface (CMI)</h2>
                <p className="text-sm text-gray-600">
                  Direct user interface to interact with EMC Partner devices
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={userRole === 'engineer' ? 'info' : 'default'}>
                <User className="w-3 h-3 mr-1" />
                {userRole}
              </Badge>
              <Badge variant={isConnected ? 'success' : 'error'}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">Device Connection</h3>
              
              {!isConnected ? (
                <Button onClick={handleConnect} className="w-full">
                  Connect to Generator
                </Button>
              ) : (
                <div className="space-y-3">
                  <div className="bg-success-50 border border-success-200 rounded-lg p-4">
                    <h4 className="font-medium text-success-900">Device Connected</h4>
                    {deviceInfo && (
                      <div className="text-sm text-success-700 mt-2 space-y-1">
                        <p>Model: {deviceInfo.model}</p>
                        <p>Serial: {deviceInfo.serialNumber}</p>
                        <p>Firmware: {deviceInfo.firmwareVersion}</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      variant="secondary"
                      onClick={runDiagnostics}
                      className="flex-1"
                    >
                      Run Diagnostics
                    </Button>
                    <Button
                      variant="error"
                      onClick={() => {
                        generator.disconnect();
                        setIsConnected(false);
                        setDeviceInfo(null);
                      }}
                      className="flex-1"
                    >
                      Disconnect
                    </Button>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">Interface Mode</h3>
              
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant={currentMode === 'basic' ? 'primary' : 'secondary'}
                    onClick={() => handleModeChange('basic')}
                    className="text-sm"
                  >
                    Basic Mode
                  </Button>
                  <Button
                    variant={currentMode === 'advanced' ? 'primary' : 'secondary'}
                    onClick={() => handleModeChange('advanced')}
                    disabled={userRole !== 'engineer'}
                    className="text-sm"
                  >
                    Advanced Mode
                  </Button>
                </div>

                <div className="text-sm text-gray-600">
                  {currentMode === 'basic' ? (
                    <div className="bg-info-50 border border-info-200 rounded-lg p-3">
                      <p className="font-medium text-info-900">Basic Mode Active</p>
                      <p className="text-info-700">
                        Guided interface with predefined test profiles and simplified controls.
                      </p>
                    </div>
                  ) : (
                    <div className="bg-warning-50 border border-warning-200 rounded-lg p-3">
                      <p className="font-medium text-warning-900">Advanced Mode Active</p>
                      <p className="text-warning-700">
                        Full SCPI command access, custom parameters, and advanced testing capabilities.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <h3 className="font-semibold text-gray-900 flex items-center">
              <Terminal className="w-4 h-4 mr-2" />
              Quick Actions
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button variant="ghost" size="sm" className="w-full justify-start">
                View Device Status
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start">
                Load Test Profile
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start">
                Execute Test
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start">
                View Test History
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="font-semibold text-gray-900 flex items-center">
              <Settings className="w-4 h-4 mr-2" />
              System Status
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Connection:</span>
                <Badge variant={isConnected ? 'success' : 'error'}>
                  {isConnected ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Mode:</span>
                <Badge variant="info">{currentMode}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">User Role:</span>
                <Badge variant="default">{userRole}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Errors:</span>
                <Badge variant="success">0</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="font-semibold text-gray-900 flex items-center">
              <HelpCircle className="w-4 h-4 mr-2" />
              Help & Support
            </h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button variant="ghost" size="sm" className="w-full justify-start">
                Command Reference
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start">
                User Manual
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start">
                Troubleshooting
              </Button>
              <Button variant="ghost" size="sm" className="w-full justify-start">
                Contact Support
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}