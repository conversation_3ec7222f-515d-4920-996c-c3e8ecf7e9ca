import React from 'react';
import { <PERSON>, <PERSON>Header, CardContent } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Thermometer, Zap, Activity, AlertTriangle, CheckCircle } from 'lucide-react';
import { useDeviceConnection } from '../../hooks/useDeviceConnection';

export function DeviceMonitor() {
  const { activeConnection, deviceStatus } = useDeviceConnection();

  if (!activeConnection) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-gray-500">
          <Activity className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No active device connection</p>
          <p className="text-sm">Connect to a device to view monitoring data</p>
        </div>
      </div>
    );
  }

  if (!deviceStatus) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-gray-500">
          <div className="animate-spin w-8 h-8 border-2 border-primary-600 border-t-transparent rounded-full mx-auto mb-4" />
          <p>Loading device status...</p>
        </div>
      </div>
    );
  }

  const statusCards = [
    {
      title: 'Temperature',
      value: `${deviceStatus.temperature.toFixed(1)}°C`,
      icon: Thermometer,
      color: deviceStatus.temperature > 40 ? 'error' : 'success',
      description: 'Operating temperature',
    },
    {
      title: 'Voltage',
      value: `${deviceStatus.voltage.toFixed(1)}V`,
      icon: Zap,
      color: 'info',
      description: 'Input voltage',
    },
    {
      title: 'Current',
      value: `${deviceStatus.current.toFixed(2)}A`,
      icon: Activity,
      color: 'info',
      description: 'Output current',
    },
    {
      title: 'Power',
      value: `${deviceStatus.power.toFixed(1)}W`,
      icon: Zap,
      color: 'info',
      description: 'Power consumption',
    },
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Device Status</h2>
              <p className="text-sm text-gray-600">{activeConnection.name} ({activeConnection.model})</p>
            </div>
            <div className="flex items-center space-x-2">
              {deviceStatus.isReady ? (
                <Badge variant="success">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Ready
                </Badge>
              ) : (
                <Badge variant="warning">
                  <AlertTriangle className="w-3 h-3 mr-1" />
                  Not Ready
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            {statusCards.map((card) => {
              const Icon = card.icon;
              return (
                <div key={card.title} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Icon className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-600">{card.title}</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">{card.value}</div>
                  <div className="text-xs text-gray-500">{card.description}</div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">System Health</h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Device Ready</span>
                <Badge variant={deviceStatus.isReady ? 'success' : 'error'}>
                  {deviceStatus.isReady ? 'Yes' : 'No'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Error Count</span>
                <Badge variant={deviceStatus.errorCount > 0 ? 'warning' : 'success'}>
                  {deviceStatus.errorCount}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Connection Status</span>
                <Badge variant="success">Connected</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last Update</span>
                <span className="text-sm text-gray-900">
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">Device Information</h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Model</span>
                <span className="text-sm font-medium text-gray-900">{activeConnection.model}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">IP Address</span>
                <span className="text-sm font-mono text-gray-900">{activeConnection.ipAddress}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Port</span>
                <span className="text-sm font-mono text-gray-900">{activeConnection.port}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Connected Since</span>
                <span className="text-sm text-gray-900">
                  {activeConnection.lastConnected?.toLocaleString()}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {deviceStatus.lastError && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-error-900">Last Error</h3>
          </CardHeader>
          <CardContent>
            <div className="bg-error-50 border border-error-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-error-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-error-900">Error Details</p>
                  <p className="text-sm text-error-700 mt-1">{deviceStatus.lastError}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}