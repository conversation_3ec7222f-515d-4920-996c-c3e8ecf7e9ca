import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON>Footer } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Play, Square, RotateCcw, AlertTriangle, CheckCircle } from 'lucide-react';
import { useTestProfiles } from '../../hooks/useTestProfiles';
import { useTestExecution } from '../../hooks/useTestExecution';
import { useDeviceConnection } from '../../hooks/useDeviceConnection';

export function TestExecution() {
  const { profiles, selectedProfile, setSelectedProfile } = useTestProfiles();
  const { currentTest, isRunning, startTest, stopTest } = useTestExecution();
  const { activeConnection } = useDeviceConnection();
  const [selectedProfileId, setSelectedProfileId] = useState<string>('');

  const handleStartTest = async () => {
    const profile = profiles.find(p => p.id === selectedProfileId);
    if (!profile) return;

    try {
      await startTest(profile.name, profile.parameters);
    } catch (error) {
      console.error('Test failed:', error);
    }
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <div className="animate-spin w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-success-600" />;
      case 'failed':
      case 'stopped':
        return <AlertTriangle className="w-4 h-4 text-error-600" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      running: 'warning',
      success: 'success',
      failed: 'error',
      stopped: 'error',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'default'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (!activeConnection) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-gray-500">
          <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No active device connection</p>
          <p className="text-sm">Connect to a device before running tests</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold text-gray-900">Test Execution</h2>
          <p className="text-sm text-gray-600">
            Select a test profile and execute tests on {activeConnection.name}
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Test Profile
            </label>
            <select
              value={selectedProfileId}
              onChange={(e) => setSelectedProfileId(e.target.value)}
              className="block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
              disabled={isRunning}
            >
              <option value="">Choose a test profile...</option>
              {profiles.map((profile) => (
                <option key={profile.id} value={profile.id}>
                  {profile.name} - {profile.parameters.level}V, {profile.parameters.polarity}
                </option>
              ))}
            </select>
          </div>

          {selectedProfileId && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">Test Parameters</h4>
              {(() => {
                const profile = profiles.find(p => p.id === selectedProfileId);
                if (!profile) return null;
                
                return (
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Coupling:</span>
                      <div className="font-medium">{profile.parameters.couplingDevice}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Level:</span>
                      <div className="font-medium">{profile.parameters.level}V</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Polarity:</span>
                      <div className="font-medium">{profile.parameters.polarity}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Impedance:</span>
                      <div className="font-medium">{profile.parameters.impedance}Ω</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Pulses:</span>
                      <div className="font-medium">{profile.parameters.numberOfPulses}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Frequency:</span>
                      <div className="font-medium">{profile.parameters.frequency || 1}Hz</div>
                    </div>
                    {profile.parameters.duration && (
                      <div>
                        <span className="text-gray-600">Duration:</span>
                        <div className="font-medium">{profile.parameters.duration}s</div>
                      </div>
                    )}
                  </div>
                );
              })()}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex space-x-3">
            <Button
              onClick={handleStartTest}
              disabled={!selectedProfileId || isRunning}
              variant="success"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Test
            </Button>
            <Button
              onClick={stopTest}
              disabled={!isRunning}
              variant="error"
            >
              <Square className="w-4 h-4 mr-2" />
              Stop Test
            </Button>
          </div>
          
          {currentTest && (
            <div className="flex items-center space-x-3">
              {getStatusIcon(currentTest.status)}
              <span className="text-sm text-gray-600">
                Duration: {formatDuration(currentTest.duration)}
              </span>
              {getStatusBadge(currentTest.status)}
            </div>
          )}
        </CardFooter>
      </Card>

      {currentTest && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Current Test</h3>
              {getStatusBadge(currentTest.status)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Profile:</span>
                  <div className="font-medium">{currentTest.profileName}</div>
                </div>
                <div>
                  <span className="text-gray-600">Started:</span>
                  <div className="font-medium">{currentTest.timestamp.toLocaleTimeString()}</div>
                </div>
                <div>
                  <span className="text-gray-600">Duration:</span>
                  <div className="font-medium">{formatDuration(currentTest.duration)}</div>
                </div>
                <div>
                  <span className="text-gray-600">Status:</span>
                  <div className="font-medium">{currentTest.status}</div>
                </div>
              </div>

              {currentTest.status === 'running' && (
                <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="animate-spin w-5 h-5 border-2 border-primary-600 border-t-transparent rounded-full" />
                    <div>
                      <p className="text-sm font-medium text-primary-900">Test in Progress</p>
                      <p className="text-sm text-primary-700">
                        Running {currentTest.profileName} test sequence...
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {currentTest.status === 'success' && (
                <div className="bg-success-50 border border-success-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-success-600" />
                    <div>
                      <p className="text-sm font-medium text-success-900">Test Completed Successfully</p>
                      <p className="text-sm text-success-700">
                        Test completed in {formatDuration(currentTest.duration)}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {(currentTest.status === 'failed' || currentTest.status === 'stopped') && (
                <div className="bg-error-50 border border-error-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <AlertTriangle className="w-5 h-5 text-error-600" />
                    <div>
                      <p className="text-sm font-medium text-error-900">
                        Test {currentTest.status === 'failed' ? 'Failed' : 'Stopped'}
                      </p>
                      {currentTest.errorMessage && (
                        <p className="text-sm text-error-700 mt-1">{currentTest.errorMessage}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}