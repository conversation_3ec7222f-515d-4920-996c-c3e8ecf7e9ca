import React from 'react';
import { cn } from '../../utils/cn';
import { 
  Wifi, 
  Play, 
  FileText, 
  BarChart3, 
  Settings, 
  Wrench,
  Monitor,
  History,
  Terminal,
  Zap
} from 'lucide-react';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  userRole: 'technician' | 'engineer';
  interfaceMode?: 'basic' | 'advanced';
}

export function Sidebar({ activeTab, onTabChange, userRole, interfaceMode = 'basic' }: SidebarProps) {
  const basicTabs = [
    { id: 'cmi', label: 'Command Interface', icon: Terminal, description: 'Main control interface' },
    { id: 'connection', label: 'Device Connection', icon: Wifi, description: 'Manage device connections' },
    { id: 'profiles', label: 'Test Profiles', icon: FileText, description: 'Predefined test configurations' },
    { id: 'execution', label: 'Test Execution', icon: Play, description: 'Run and monitor tests' },
    { id: 'monitoring', label: 'Device Monitor', icon: Monitor, description: 'Real-time device status' },
    { id: 'history', label: 'Test History', icon: History, description: 'View past test results' },
  ];

  const advancedTabs = [
    { id: 'parameters', label: 'Custom Parameters', icon: Settings, description: 'Advanced parameter control' },
    { id: 'commands', label: 'SCPI Commands', icon: Wrench, description: 'Direct command interface' },
    { id: 'analytics', label: 'Analytics', icon: BarChart3, description: 'Test data analysis' },
  ];

  const availableTabs = userRole === 'engineer' && interfaceMode === 'advanced' 
    ? [...basicTabs, ...advancedTabs] 
    : basicTabs;

  return (
    <aside className="w-64 bg-gray-50 border-r border-gray-200 h-full">
      <nav className="p-4 space-y-2">
        <div className="mb-6">
          <div className="flex items-center space-x-2 mb-4">
            <div className="flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg">
              <Zap className="w-4 h-4 text-white" />
            </div>
            <div>
              <h2 className="text-sm font-semibold text-gray-900">PyEMC Control</h2>
              <p className="text-xs text-gray-500">v2.0.0</p>
            </div>
          </div>
          
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
            Navigation
          </h3>
          
          {availableTabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={cn(
                  'w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors group',
                  isActive
                    ? 'bg-primary-100 text-primary-700 border border-primary-200'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                )}
                title={tab.description}
              >
                <Icon className={cn(
                  'w-4 h-4 transition-colors',
                  isActive ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-600'
                )} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {userRole === 'engineer' && (
          <div className="pt-4 border-t border-gray-200">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
              Mode: {interfaceMode}
            </h3>
            <div className={cn(
              "text-xs p-3 rounded-lg border",
              interfaceMode === 'advanced' 
                ? "text-warning-800 bg-warning-50 border-warning-200"
                : "text-info-800 bg-info-50 border-info-200"
            )}>
              <p className="font-medium mb-1">
                {interfaceMode === 'advanced' ? 'Advanced Mode' : 'Basic Mode'}
              </p>
              <p>
                {interfaceMode === 'advanced' 
                  ? 'Full SCPI access and custom parameters enabled.'
                  : 'Guided interface with predefined profiles active.'
                }
              </p>
            </div>
          </div>
        )}

        <div className="pt-4 border-t border-gray-200">
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">
            Architecture
          </h3>
          <div className="text-xs text-gray-600 space-y-1">
            <div className="flex justify-between">
              <span>Core:</span>
              <span className="font-mono">GeneratorBase</span>
            </div>
            <div className="flex justify-between">
              <span>Tests:</span>
              <span className="font-mono">TestManager</span>
            </div>
            <div className="flex justify-between">
              <span>UI:</span>
              <span className="font-mono">CMI</span>
            </div>
            <div className="flex justify-between">
              <span>Errors:</span>
              <span className="font-mono">ErrorHandler</span>
            </div>
          </div>
        </div>
      </nav>
    </aside>
  );
}