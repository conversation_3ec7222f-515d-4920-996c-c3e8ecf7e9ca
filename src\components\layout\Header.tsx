import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, User, HelpCircle } from 'lucide-react';
import { Button } from '../ui/Button';

interface HeaderProps {
  currentUser: { name: string; role: string };
  onSettingsClick: () => void;
  onHelpClick: () => void;
}

export function Header({ currentUser, onSettingsClick, onHelpClick }: HeaderProps) {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-primary-600 rounded-lg">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">PyEMC Control</h1>
            <p className="text-sm text-gray-500">EMC Partner Generator Control Software</p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <User className="w-4 h-4" />
            <span>{currentUser.name}</span>
            <span className="px-2 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium">
              {currentUser.role}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onHelpClick}
              className="p-2"
            >
              <HelpCircle className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onSettingsClick}
              className="p-2"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}