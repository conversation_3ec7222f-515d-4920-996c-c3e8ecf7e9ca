import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Badge } from '../ui/Badge';
import { Wifi, WifiOff, Loader, Trash2, Power } from 'lucide-react';
import { DeviceConnection as DeviceConnectionType, GeneratorModel } from '../../types';
import { useDeviceConnection } from '../../hooks/useDeviceConnection';

const generatorModels: Array<{ value: GeneratorModel; label: string }> = [
  { value: 'IMU-4000', label: 'IMU-4000' },
  { value: 'IMU-3000', label: 'IMU-3000' },
  { value: 'IMU-MG1', label: 'IMU-MG1' },
  { value: 'DOW-3000-S-F', label: 'DOW-3000-S-F' },
  { value: 'DOW-CG1', label: 'DOW-CG1' },
];

export function DeviceConnection() {
  const {
    connections,
    activeConnection,
    isConnecting,
    connect,
    disconnect,
    removeConnection,
    setActiveConnection,
  } = useDeviceConnection();

  const [formData, setFormData] = useState({
    name: '',
    ipAddress: '************',
    port: 50500,
    model: 'IMU-3000' as GeneratorModel,
  });

  const handleConnect = async () => {
    try {
      await connect(formData);
      setFormData({
        name: '',
        ipAddress: '************',
        port: 50500,
        model: 'IMU-3000',
      });
    } catch (error) {
      console.error('Connection failed:', error);
    }
  };

  const getStatusBadge = (status: DeviceConnectionType['status']) => {
    const variants = {
      connected: 'success',
      disconnected: 'default',
      connecting: 'warning',
      error: 'error',
    } as const;

    const labels = {
      connected: 'Connected',
      disconnected: 'Disconnected',
      connecting: 'Connecting...',
      error: 'Error',
    };

    return (
      <Badge variant={variants[status]}>
        {labels[status]}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold text-gray-900">Add New Device</h2>
          <p className="text-sm text-gray-600">
            Connect to an EMC Partner generator on your network
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Device Name"
              placeholder="e.g., Lab Generator 1"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            />
            <Select
              label="Generator Model"
              value={formData.model}
              onChange={(e) => setFormData({ ...formData, model: e.target.value as GeneratorModel })}
              options={generatorModels}
            />
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <div className="col-span-2">
              <Input
                label="IP Address"
                placeholder="************"
                value={formData.ipAddress}
                onChange={(e) => setFormData({ ...formData, ipAddress: e.target.value })}
              />
            </div>
            <Input
              label="Port"
              type="number"
              value={formData.port}
              onChange={(e) => setFormData({ ...formData, port: parseInt(e.target.value) })}
            />
          </div>

          <Button
            onClick={handleConnect}
            isLoading={isConnecting}
            disabled={!formData.name || !formData.ipAddress}
            className="w-full"
          >
            <Wifi className="w-4 h-4 mr-2" />
            Connect Device
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold text-gray-900">Connected Devices</h2>
          <p className="text-sm text-gray-600">
            Manage your device connections
          </p>
        </CardHeader>
        <CardContent>
          {connections.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <WifiOff className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No devices connected</p>
              <p className="text-sm">Add a device connection above to get started</p>
            </div>
          ) : (
            <div className="space-y-3">
              {connections.map((connection) => (
                <div
                  key={connection.id}
                  className={`p-4 border rounded-lg transition-colors ${
                    activeConnection?.id === connection.id
                      ? 'border-primary-200 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-lg">
                        {connection.status === 'connected' ? (
                          <Wifi className="w-5 h-5 text-success-600" />
                        ) : connection.status === 'connecting' ? (
                          <Loader className="w-5 h-5 text-warning-600 animate-spin" />
                        ) : (
                          <WifiOff className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{connection.name}</h3>
                        <p className="text-sm text-gray-500">
                          {connection.model} • {connection.ipAddress}:{connection.port}
                        </p>
                        {connection.lastConnected && (
                          <p className="text-xs text-gray-400">
                            Last connected: {connection.lastConnected.toLocaleString()}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      {getStatusBadge(connection.status)}
                      
                      <div className="flex items-center space-x-1">
                        {connection.status === 'connected' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setActiveConnection(connection)}
                            disabled={activeConnection?.id === connection.id}
                          >
                            <Power className="w-4 h-4" />
                          </Button>
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => 
                            connection.status === 'connected' 
                              ? disconnect(connection.id)
                              : connect(connection)
                          }
                          disabled={connection.status === 'connecting'}
                        >
                          {connection.status === 'connected' ? 'Disconnect' : 'Connect'}
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeConnection(connection.id)}
                          className="text-error-600 hover:text-error-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}