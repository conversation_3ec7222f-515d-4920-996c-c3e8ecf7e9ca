/**
 * TestManager.ts - Test execution and parameter management
 * Equivalent to TestManager.py in the original project
 * Handles test execution, parameter management, and user interactions
 */

import { GeneratorBase } from './GeneratorBase';
import { TestParameters, TestResult } from '../types';

export interface TestConfiguration {
  name: string;
  description: string;
  parameters: TestParameters;
  preTestCommands?: string[];
  postTestCommands?: string[];
}

export class TestManager {
  private generator: GeneratorBase;
  private currentTest: TestConfiguration | null = null;
  private isRunning: boolean = false;
  private testResults: TestResult[] = [];

  constructor(generator: GeneratorBase) {
    this.generator = generator;
  }

  /**
   * Set test configuration
   */
  setTestConfiguration(config: TestConfiguration): void {
    this.currentTest = config;
  }

  /**
   * Get current test configuration
   */
  getCurrentTest(): TestConfiguration | null {
    return this.currentTest;
  }

  /**
   * Execute pre-configured test
   */
  async executeTest(interactive: boolean = false): Promise<TestResult> {
    if (!this.currentTest) {
      throw new Error('No test configuration set');
    }

    if (!this.generator.isDeviceConnected()) {
      throw new Error('Generator not connected');
    }

    this.isRunning = true;
    const startTime = Date.now();

    const testResult: TestResult = {
      id: Date.now().toString(),
      timestamp: new Date(),
      profileName: this.currentTest.name,
      parameters: this.currentTest.parameters,
      status: 'running',
      duration: 0,
    };

    try {
      // Execute pre-test commands
      if (this.currentTest.preTestCommands) {
        for (const command of this.currentTest.preTestCommands) {
          await this.generator.sendCommand(command);
        }
      }

      // Set test parameters
      await this.setTestParameters(this.currentTest.parameters);

      // Start test execution
      await this.generator.sendCommand('RUN:START');

      // Monitor test execution
      await this.monitorTestExecution(testResult);

      // Execute post-test commands
      if (this.currentTest.postTestCommands) {
        for (const command of this.currentTest.postTestCommands) {
          await this.generator.sendCommand(command);
        }
      }

      testResult.status = 'success';
      testResult.duration = Date.now() - startTime;

    } catch (error) {
      testResult.status = 'failed';
      testResult.duration = Date.now() - startTime;
      testResult.errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw error;
    } finally {
      this.isRunning = false;
      this.testResults.push(testResult);
    }

    return testResult;
  }

  /**
   * Stop currently running test
   */
  async stopTest(): Promise<void> {
    if (this.isRunning) {
      await this.generator.sendCommand('RUN:STOP');
      this.isRunning = false;
    }
  }

  /**
   * Set test parameters on the device
   */
  private async setTestParameters(parameters: TestParameters): Promise<void> {
    // Set active test type
    await this.generator.sendCommand(`TEST:ACTIVE ${this.getTestTypeFromParameters(parameters)}`);

    // Set test-specific parameters
    if (parameters.level) {
      await this.generator.sendCommand(`TEST:CWG:LEVEL:VAL ${parameters.level}`);
    }

    // Set power configuration
    const powerSetting = this.mapCouplingToPower(parameters.couplingDevice);
    await this.generator.sendCommand(`PWR:SET ${powerSetting}`);

    // Additional parameter mapping would go here
  }

  /**
   * Monitor test execution progress
   */
  private async monitorTestExecution(testResult: TestResult): Promise<void> {
    const maxDuration = 30000; // 30 seconds max
    const checkInterval = 500; // Check every 500ms
    let elapsed = 0;

    while (elapsed < maxDuration) {
      const state = await this.generator.sendCommand('RUN:STATE?');
      
      if (state === 'IDLE') {
        // Test completed
        break;
      }

      if (state === 'ERROR') {
        const error = await this.generator.sendCommand('RUN:ERROR?');
        throw new Error(`Test failed: ${error}`);
      }

      // Update progress
      testResult.duration = elapsed;

      await new Promise(resolve => setTimeout(resolve, checkInterval));
      elapsed += checkInterval;
    }

    if (elapsed >= maxDuration) {
      throw new Error('Test timeout');
    }
  }

  /**
   * Get test results history
   */
  getTestResults(): TestResult[] {
    return [...this.testResults];
  }

  /**
   * Clear test results history
   */
  clearTestResults(): void {
    this.testResults = [];
  }

  /**
   * Check if test is currently running
   */
  isTestRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Map test parameters to device test type
   */
  private getTestTypeFromParameters(parameters: TestParameters): string {
    // This would contain logic to determine test type based on parameters
    // For now, defaulting to CWG
    return 'CWG';
  }

  /**
   * Map coupling device to power setting
   */
  private mapCouplingToPower(coupling: string): string {
    switch (coupling.toUpperCase()) {
      case 'INTERN':
        return 'PWR1';
      case 'EXTERN':
        return 'PWR2';
      default:
        return 'OFF';
    }
  }

  /**
   * Validate test parameters
   */
  validateParameters(parameters: TestParameters): string[] {
    const errors: string[] = [];

    if (!parameters.level || parameters.level <= 0) {
      errors.push('Level must be greater than 0');
    }

    if (parameters.level && parameters.level > 5000) {
      errors.push('Level cannot exceed 5000V');
    }

    if (!parameters.numberOfPulses || parameters.numberOfPulses <= 0) {
      errors.push('Number of pulses must be greater than 0');
    }

    return errors;
  }
}