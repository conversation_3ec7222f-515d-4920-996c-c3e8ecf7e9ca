import { useState, useCallback } from 'react';
import { TestProfile, TestParameters } from '../types';

const defaultProfiles: TestProfile[] = [
  {
    id: '1',
    name: 'Standard Surge Test',
    description: 'Basic surge immunity test with standard parameters',
    isCustom: false,
    parameters: {
      couplingDevice: 'INTERN',
      level: 1000,
      polarity: 'POSITIVE',
      impedance: '50',
      numberOfPulses: 5,
      frequency: 1,
    },
  },
  {
    id: '2',
    name: 'High Level Burst',
    description: 'High-level electrical fast transient burst test',
    isCustom: false,
    parameters: {
      couplingDevice: 'EXTERN',
      level: 2000,
      polarity: 'BOTH',
      impedance: '50',
      numberOfPulses: 100,
      frequency: 5,
      duration: 60,
    },
  },
  {
    id: '3',
    name: 'Low Impedance Test',
    description: 'Surge test with low impedance coupling',
    isCustom: false,
    parameters: {
      couplingDevice: 'INTERN',
      level: 500,
      polarity: 'NEGATIVE',
      impedance: '2',
      numberOfPulses: 10,
      frequency: 1,
    },
  },
];

export function useTestProfiles() {
  const [profiles, setProfiles] = useState<TestProfile[]>(defaultProfiles);
  const [selectedProfile, setSelectedProfile] = useState<TestProfile | null>(null);

  const createProfile = useCallback((name: string, description: string, parameters: TestParameters) => {
    const newProfile: TestProfile = {
      id: Date.now().toString(),
      name,
      description,
      parameters,
      isCustom: true,
    };

    setProfiles(prev => [...prev, newProfile]);
    return newProfile;
  }, []);

  const updateProfile = useCallback((profileId: string, updates: Partial<TestProfile>) => {
    setProfiles(prev => 
      prev.map(profile => 
        profile.id === profileId 
          ? { ...profile, ...updates }
          : profile
      )
    );
  }, []);

  const deleteProfile = useCallback((profileId: string) => {
    setProfiles(prev => prev.filter(profile => profile.id !== profileId));
    
    if (selectedProfile?.id === profileId) {
      setSelectedProfile(null);
    }
  }, [selectedProfile]);

  const duplicateProfile = useCallback((profileId: string) => {
    const profile = profiles.find(p => p.id === profileId);
    if (!profile) return;

    const duplicated: TestProfile = {
      ...profile,
      id: Date.now().toString(),
      name: `${profile.name} (Copy)`,
      isCustom: true,
    };

    setProfiles(prev => [...prev, duplicated]);
    return duplicated;
  }, [profiles]);

  return {
    profiles,
    selectedProfile,
    setSelectedProfile,
    createProfile,
    updateProfile,
    deleteProfile,
    duplicateProfile,
  };
}