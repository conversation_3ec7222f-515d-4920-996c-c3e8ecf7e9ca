import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { FileText, Plus, Edit, Trash2, Copy, Play } from 'lucide-react';
import { useTestProfiles } from '../../hooks/useTestProfiles';
import { TestParameters } from '../../types';

export function TestProfiles() {
  const {
    profiles,
    selectedProfile,
    setSelectedProfile,
    createProfile,
    updateProfile,
    deleteProfile,
    duplicateProfile,
  } = useTestProfiles();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingProfile, setEditingProfile] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    parameters: {
      couplingDevice: 'INTERN',
      level: 1000,
      polarity: 'POSITIVE' as const,
      impedance: '50',
      numberOfPulses: 5,
      frequency: 1,
      duration: 60,
    } as TestParameters,
  });

  const handleCreateProfile = () => {
    createProfile(formData.name, formData.description, formData.parameters);
    setShowCreateForm(false);
    setFormData({
      name: '',
      description: '',
      parameters: {
        couplingDevice: 'INTERN',
        level: 1000,
        polarity: 'POSITIVE',
        impedance: '50',
        numberOfPulses: 5,
        frequency: 1,
        duration: 60,
      },
    });
  };

  const handleEditProfile = (profileId: string) => {
    const profile = profiles.find(p => p.id === profileId);
    if (profile) {
      setFormData({
        name: profile.name,
        description: profile.description,
        parameters: profile.parameters,
      });
      setEditingProfile(profileId);
      setShowCreateForm(true);
    }
  };

  const handleUpdateProfile = () => {
    if (editingProfile) {
      updateProfile(editingProfile, {
        name: formData.name,
        description: formData.description,
        parameters: formData.parameters,
      });
      setEditingProfile(null);
      setShowCreateForm(false);
    }
  };

  const couplingOptions = [
    { value: 'INTERN', label: 'Internal' },
    { value: 'EXTERN', label: 'External' },
  ];

  const polarityOptions = [
    { value: 'POSITIVE', label: 'Positive' },
    { value: 'NEGATIVE', label: 'Negative' },
    { value: 'BOTH', label: 'Both' },
  ];

  const impedanceOptions = [
    { value: '2', label: '2Ω' },
    { value: '12', label: '12Ω' },
    { value: '50', label: '50Ω' },
    { value: '150', label: '150Ω' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Test Profiles</h2>
          <p className="text-sm text-gray-600">
            Manage predefined and custom test configurations
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Profile
        </Button>
      </div>

      {showCreateForm && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              {editingProfile ? 'Edit Profile' : 'Create New Profile'}
            </h3>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Profile Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., Custom Surge Test"
              />
              <Input
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Brief description of the test"
              />
            </div>

            <div className="border-t pt-4">
              <h4 className="font-medium text-gray-900 mb-4">Test Parameters</h4>
              <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
                <Select
                  label="Coupling Device"
                  value={formData.parameters.couplingDevice}
                  onChange={(e) => setFormData({
                    ...formData,
                    parameters: { ...formData.parameters, couplingDevice: e.target.value }
                  })}
                  options={couplingOptions}
                />
                <Input
                  label="Level (V)"
                  type="number"
                  value={formData.parameters.level}
                  onChange={(e) => setFormData({
                    ...formData,
                    parameters: { ...formData.parameters, level: parseInt(e.target.value) }
                  })}
                />
                <Select
                  label="Polarity"
                  value={formData.parameters.polarity}
                  onChange={(e) => setFormData({
                    ...formData,
                    parameters: { ...formData.parameters, polarity: e.target.value as any }
                  })}
                  options={polarityOptions}
                />
                <Select
                  label="Impedance"
                  value={formData.parameters.impedance}
                  onChange={(e) => setFormData({
                    ...formData,
                    parameters: { ...formData.parameters, impedance: e.target.value }
                  })}
                  options={impedanceOptions}
                />
                <Input
                  label="Number of Pulses"
                  type="number"
                  value={formData.parameters.numberOfPulses}
                  onChange={(e) => setFormData({
                    ...formData,
                    parameters: { ...formData.parameters, numberOfPulses: parseInt(e.target.value) }
                  })}
                />
                <Input
                  label="Frequency (Hz)"
                  type="number"
                  value={formData.parameters.frequency || 1}
                  onChange={(e) => setFormData({
                    ...formData,
                    parameters: { ...formData.parameters, frequency: parseInt(e.target.value) }
                  })}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-3">
            <Button
              variant="secondary"
              onClick={() => {
                setShowCreateForm(false);
                setEditingProfile(null);
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={editingProfile ? handleUpdateProfile : handleCreateProfile}
              disabled={!formData.name || !formData.description}
            >
              {editingProfile ? 'Update Profile' : 'Create Profile'}
            </Button>
          </CardFooter>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
        {profiles.map((profile) => (
          <Card
            key={profile.id}
            className={`cursor-pointer transition-all ${
              selectedProfile?.id === profile.id
                ? 'ring-2 ring-primary-500 border-primary-200'
                : 'hover:shadow-md'
            }`}
            onClick={() => setSelectedProfile(profile)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-primary-100 rounded-lg">
                    <FileText className="w-5 h-5 text-primary-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{profile.name}</h3>
                    <p className="text-sm text-gray-600 mt-1">{profile.description}</p>
                  </div>
                </div>
                <Badge variant={profile.isCustom ? 'info' : 'default'}>
                  {profile.isCustom ? 'Custom' : 'Standard'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Level:</span>
                  <span className="font-medium">{profile.parameters.level}V</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Polarity:</span>
                  <span className="font-medium">{profile.parameters.polarity}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Impedance:</span>
                  <span className="font-medium">{profile.parameters.impedance}Ω</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Pulses:</span>
                  <span className="font-medium">{profile.parameters.numberOfPulses}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="flex space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    duplicateProfile(profile.id);
                  }}
                >
                  <Copy className="w-3 h-3" />
                </Button>
                {profile.isCustom && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditProfile(profile.id);
                      }}
                    >
                      <Edit className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteProfile(profile.id);
                      }}
                      className="text-error-600 hover:text-error-700"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </>
                )}
              </div>
              <Button
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  // This would trigger test execution
                  console.log('Run test with profile:', profile);
                }}
              >
                <Play className="w-3 h-3 mr-1" />
                Run
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {profiles.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No test profiles available</p>
          <p className="text-sm">Create your first test profile to get started</p>
        </div>
      )}
    </div>
  );
}