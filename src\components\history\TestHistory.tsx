import React from 'react';
import { <PERSON>, <PERSON>Header, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { History, Download, Trash2, CheckCircle, AlertTriangle, Square } from 'lucide-react';
import { useTestExecution } from '../../hooks/useTestExecution';

export function TestHistory() {
  const { testHistory, clearHistory } = useTestExecution();

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-success-600" />;
      case 'failed':
        return <AlertTriangle className="w-4 h-4 text-error-600" />;
      case 'stopped':
        return <Square className="w-4 h-4 text-error-600" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'success',
      failed: 'error',
      stopped: 'error',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'default'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const exportResults = () => {
    const csvContent = [
      ['Timestamp', 'Profile', 'Status', 'Duration (s)', 'Level (V)', 'Polarity', 'Impedance', 'Pulses', 'Error'].join(','),
      ...testHistory.map(test => [
        test.timestamp.toISOString(),
        test.profileName,
        test.status,
        (test.duration / 1000).toFixed(2),
        test.parameters.level,
        test.parameters.polarity,
        test.parameters.impedance,
        test.parameters.numberOfPulses,
        test.errorMessage || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `test-results-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Test History</h2>
          <p className="text-sm text-gray-600">
            View and manage previous test results
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={exportResults}
            disabled={testHistory.length === 0}
          >
            <Download className="w-4 h-4 mr-2" />
            Export CSV
          </Button>
          <Button
            variant="error"
            onClick={clearHistory}
            disabled={testHistory.length === 0}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Clear History
          </Button>
        </div>
      </div>

      {testHistory.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <History className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No test history available</p>
          <p className="text-sm">Run some tests to see results here</p>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900">
              Test Results ({testHistory.length})
            </h3>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Profile</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Timestamp</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Duration</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Parameters</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Error</th>
                  </tr>
                </thead>
                <tbody>
                  {testHistory.map((test) => (
                    <tr key={test.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(test.status)}
                          {getStatusBadge(test.status)}
                        </div>
                      </td>
                      <td className="py-3 px-4 font-medium text-gray-900">
                        {test.profileName}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {test.timestamp.toLocaleString()}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {formatDuration(test.duration)}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        <div className="text-xs space-y-1">
                          <div>{test.parameters.level}V, {test.parameters.polarity}</div>
                          <div>{test.parameters.impedance}Ω, {test.parameters.numberOfPulses} pulses</div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {test.errorMessage ? (
                          <span className="text-error-600 text-xs">{test.errorMessage}</span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}