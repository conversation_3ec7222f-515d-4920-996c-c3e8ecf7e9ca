import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/Badge';
import { Terminal, Send, History, Copy, Trash2, BookOpen, AlertTriangle } from 'lucide-react';
import { useDeviceConnection } from '../../hooks/useDeviceConnection';
import { useSCPICommands } from '../../hooks/useSCPICommands';

export function SCPICommands() {
  const { activeConnection } = useDeviceConnection();
  const {
    commandHistory,
    isExecuting,
    executeCommand,
    clearHistory,
    getCommandSuggestions,
  } = useSCPICommands();

  const [currentCommand, setCurrentCommand] = useState('');
  const [showHelp, setShowHelp] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const terminalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [commandHistory]);

  const handleExecuteCommand = async () => {
    if (!currentCommand.trim()) return;
    
    try {
      await executeCommand(currentCommand.trim());
      setCurrentCommand('');
    } catch (error) {
      console.error('Command execution failed:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleExecuteCommand();
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const commandCategories = [
    { id: 'all', label: 'All Commands' },
    { id: 'root', label: 'Root Commands' },
    { id: 'test', label: 'Test Commands' },
    { id: 'run', label: 'Run Commands' },
    { id: 'status', label: 'Status Commands' },
    { id: 'power', label: 'Power Commands' },
    { id: 'system', label: 'System Commands' },
    { id: 'environment', label: 'Environment Commands' },
    { id: 'protocol', label: 'Protocol Commands' },
    { id: 'audio', label: 'Audio Commands' },
  ];

  const commonCommands = [
    { command: '*IDN?', description: 'Get device identification' },
    { command: '*LRN?', description: 'Get current generator setup' },
    { command: 'TEST:AVAIL?', description: 'Get available tests' },
    { command: 'TEST:ACTIVE?', description: 'Get currently active test' },
    { command: 'RUN:STATE?', description: 'Get current test state' },
    { command: 'RUN:START', description: 'Start currently active test' },
    { command: 'RUN:STOP', description: 'Stop currently running test' },
    { command: 'PWR:SET?', description: 'Get current power setting' },
    { command: 'SYSTEM:INFO:VERSION?', description: 'Get EPOS version' },
    { command: 'ENVIRONMENT:TEMPERATURE?', description: 'Get current temperature' },
  ];

  const getStatusBadge = (status: 'success' | 'error' | 'info') => {
    const variants = {
      success: 'success',
      error: 'error',
      info: 'info',
    } as const;

    return variants[status];
  };

  if (!activeConnection) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-gray-500">
          <Terminal className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No active device connection</p>
          <p className="text-sm">Connect to a device before using SCPI commands</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">SCPI Command Interface</h2>
          <p className="text-sm text-gray-600">
            Direct command execution for {activeConnection.name}
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={() => setShowHelp(!showHelp)}
          >
            <BookOpen className="w-4 h-4 mr-2" />
            Command Reference
          </Button>
          <Button
            variant="error"
            onClick={clearHistory}
            disabled={commandHistory.length === 0}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Clear History
          </Button>
        </div>
      </div>

      {showHelp && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Command Reference</h3>
              <div className="flex space-x-2">
                {commandCategories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                  >
                    {category.label}
                  </Button>
                ))}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-warning-50 border border-warning-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-5 h-5 text-warning-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-warning-900">Important Notes</p>
                    <ul className="text-sm text-warning-700 mt-1 space-y-1">
                      <li>• Commands ending with '?' are queries that return values</li>
                      <li>• Commands without '?' are setters that change device state</li>
                      <li>• Use quoted strings for text parameters: "example text"</li>
                      <li>• Some commands require specific test modes to be active</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">Common Commands</h4>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                  {commonCommands.map((cmd, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                      onClick={() => setCurrentCommand(cmd.command)}
                    >
                      <div>
                        <code className="text-sm font-mono text-primary-600">{cmd.command}</code>
                        <p className="text-xs text-gray-600 mt-1">{cmd.description}</p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          copyToClipboard(cmd.command);
                        }}
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">Command Terminal</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div
                  ref={terminalRef}
                  className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm h-96 overflow-y-auto"
                >
                  {commandHistory.length === 0 ? (
                    <div className="text-gray-500">
                      <p>SCPI Command Terminal - Ready</p>
                      <p>Connected to: {activeConnection.name} ({activeConnection.model})</p>
                      <p>Type commands below or use the reference panel for help.</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {commandHistory.map((entry, index) => (
                        <div key={index} className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className="text-blue-400">{'>'}</span>
                            <span className="text-white">{entry.command}</span>
                            <span className="text-xs text-gray-500">
                              {entry.timestamp.toLocaleTimeString()}
                            </span>
                          </div>
                          {entry.response && (
                            <div className="ml-4 text-green-400">
                              {entry.response}
                            </div>
                          )}
                          {entry.error && (
                            <div className="ml-4 text-red-400">
                              Error: {entry.error}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="flex space-x-2">
                  <Input
                    value={currentCommand}
                    onChange={(e) => setCurrentCommand(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Enter SCPI command (e.g., *IDN?)"
                    className="font-mono"
                    disabled={isExecuting}
                  />
                  <Button
                    onClick={handleExecuteCommand}
                    disabled={!currentCommand.trim() || isExecuting}
                    isLoading={isExecuting}
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Send
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">Command History</h3>
            </CardHeader>
            <CardContent>
              {commandHistory.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <History className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">No commands executed yet</p>
                </div>
              ) : (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {commandHistory.slice(-10).reverse().map((entry, index) => (
                    <div
                      key={index}
                      className="p-2 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => setCurrentCommand(entry.command)}
                    >
                      <div className="flex items-center justify-between">
                        <code className="text-xs font-mono text-gray-700">
                          {entry.command}
                        </code>
                        <Badge variant={getStatusBadge(entry.status)}>
                          {entry.status}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {entry.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="mt-4">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">Device Status</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Connection:</span>
                  <Badge variant="success">Connected</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Model:</span>
                  <span className="font-medium">{activeConnection.model}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Address:</span>
                  <span className="font-mono text-xs">{activeConnection.ipAddress}:{activeConnection.port}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Commands Sent:</span>
                  <span className="font-medium">{commandHistory.length}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}