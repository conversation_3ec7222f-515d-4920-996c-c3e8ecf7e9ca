/**
 * PowerUser.ts - Advanced functionality for power users
 * Equivalent to PowerUser.py in the original project
 * Class focused on advanced features for engineers
 */

import { GeneratorBase } from './GeneratorBase';
import { TestManager } from './TestManager';
import { ErrorHandler } from './ErrorHandling';

export interface AdvancedTestConfiguration {
  name: string;
  description: string;
  commands: string[];
  parameters: Record<string, any>;
  validationRules: ValidationRule[];
  executionMode: 'sequential' | 'parallel' | 'conditional';
  retryPolicy?: RetryPolicy;
}

export interface ValidationRule {
  parameter: string;
  condition: string;
  value: any;
  errorMessage: string;
}

export interface RetryPolicy {
  maxRetries: number;
  retryDelay: number;
  retryConditions: string[];
}

export interface ParametricTestConfig {
  baseTest: AdvancedTestConfiguration;
  sweepParameters: SweepParameter[];
  stopConditions: StopCondition[];
  dataCollection: DataCollectionConfig;
}

export interface SweepParameter {
  name: string;
  startValue: number;
  endValue: number;
  stepSize: number;
  stepType: 'linear' | 'logarithmic';
}

export interface StopCondition {
  type: 'error' | 'threshold' | 'time' | 'manual';
  condition: string;
  value?: any;
}

export interface DataCollectionConfig {
  measurements: string[];
  samplingRate: number;
  duration: number;
  triggers: string[];
}

export class PowerUser {
  private generator: GeneratorBase;
  private testManager: TestManager;
  private errorHandler: ErrorHandler;
  private customCommands: Map<string, string[]> = new Map();

  constructor(generator: GeneratorBase, testManager: TestManager) {
    this.generator = generator;
    this.testManager = testManager;
    this.errorHandler = ErrorHandler.getInstance();
  }

  /**
   * Execute custom command sequence
   */
  async executeCommandSequence(
    commands: string[],
    options: {
      stopOnError?: boolean;
      timeout?: number;
      retryCount?: number;
    } = {}
  ): Promise<string[]> {
    const results: string[] = [];
    const { stopOnError = true, timeout = 5000, retryCount = 0 } = options;

    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      let attempts = 0;
      let success = false;

      while (attempts <= retryCount && !success) {
        try {
          const result = await Promise.race([
            this.generator.sendCommand(command),
            new Promise<never>((_, reject) => 
              setTimeout(() => reject(new Error('Command timeout')), timeout)
            )
          ]);

          results.push(result);
          success = true;

        } catch (error) {
          attempts++;
          const errorMsg = error instanceof Error ? error.message : 'Unknown error';
          
          if (attempts > retryCount) {
            this.errorHandler.logDeviceError(command, errorMsg);
            
            if (stopOnError) {
              throw new Error(`Command failed after ${retryCount + 1} attempts: ${command}`);
            } else {
              results.push(`ERROR: ${errorMsg}`);
              success = true; // Continue with next command
            }
          } else {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }
    }

    return results;
  }

  /**
   * Create and save custom command macro
   */
  saveCommandMacro(name: string, commands: string[]): void {
    this.customCommands.set(name, [...commands]);
  }

  /**
   * Execute saved command macro
   */
  async executeCommandMacro(name: string): Promise<string[]> {
    const commands = this.customCommands.get(name);
    if (!commands) {
      throw new Error(`Command macro '${name}' not found`);
    }

    return this.executeCommandSequence(commands);
  }

  /**
   * Get all saved command macros
   */
  getCommandMacros(): Record<string, string[]> {
    return Object.fromEntries(this.customCommands);
  }

  /**
   * Execute parametric test
   */
  async executeParametricTest(config: ParametricTestConfig): Promise<any[]> {
    const results: any[] = [];
    
    // Generate parameter combinations
    const parameterSets = this.generateParameterSets(config.sweepParameters);
    
    for (let i = 0; i < parameterSets.length; i++) {
      const paramSet = parameterSets[i];
      
      try {
        // Check stop conditions
        if (this.checkStopConditions(config.stopConditions, results)) {
          break;
        }

        // Apply parameters
        await this.applyParameterSet(paramSet);

        // Execute test
        const testResult = await this.testManager.executeTest();

        // Collect additional data if configured
        const additionalData = await this.collectData(config.dataCollection);

        results.push({
          parameters: paramSet,
          testResult,
          additionalData,
          timestamp: new Date(),
        });

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        this.errorHandler.logTestError(`Parametric test step ${i + 1}`, errorMsg);
        
        results.push({
          parameters: paramSet,
          error: errorMsg,
          timestamp: new Date(),
        });
      }
    }

    return results;
  }

  /**
   * Advanced device diagnostics
   */
  async runDiagnostics(): Promise<{
    deviceInfo: any;
    systemStatus: any;
    errorLog: any[];
    performanceMetrics: any;
  }> {
    const diagnostics = {
      deviceInfo: {},
      systemStatus: {},
      errorLog: [],
      performanceMetrics: {},
    };

    try {
      // Device information
      diagnostics.deviceInfo = {
        identification: await this.generator.sendCommand('*IDN?'),
        version: await this.generator.sendCommand('SYSTEM:INFO:VERSION?'),
        serial: await this.generator.sendCommand('SYSTEM:INFO:SERIAL?'),
        hardwareType: await this.generator.sendCommand('SYSTEM:INFO:HARDWARE_TYPE?'),
      };

      // System status
      diagnostics.systemStatus = {
        temperature: await this.generator.sendCommand('ENVIRONMENT:TEMPERATURE?'),
        humidity: await this.generator.sendCommand('ENVIRONMENT:HUMIDITY?'),
        powerStatus: await this.generator.sendCommand('PWR:SET?'),
        safetyCircuit: await this.generator.sendCommand('PWR:SAFETY_CIRCUIT?'),
        runState: await this.generator.sendCommand('RUN:STATE?'),
      };

      // Error log
      const lastError = await this.generator.sendCommand('RUN:ERROR?');
      const lastWarning = await this.generator.sendCommand('RUN:WARNING?');
      
      if (lastError !== 'NO_ERROR') {
        diagnostics.errorLog.push({ type: 'error', message: lastError });
      }
      
      if (lastWarning !== 'NO_WARNING') {
        diagnostics.errorLog.push({ type: 'warning', message: lastWarning });
      }

      // Performance metrics
      diagnostics.performanceMetrics = {
        totalTestTime: await this.generator.sendCommand('RUN:TIME:TOTAL?'),
        currentTestTime: await this.generator.sendCommand('RUN:TIME:CURRENT?'),
        cycleTime: await this.generator.sendCommand('RUN:CYCLE:TIME?'),
      };

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      this.errorHandler.logDeviceError('Diagnostics', errorMsg);
      throw error;
    }

    return diagnostics;
  }

  /**
   * Generate parameter sets for parametric testing
   */
  private generateParameterSets(sweepParams: SweepParameter[]): Record<string, number>[] {
    if (sweepParams.length === 0) return [{}];

    const parameterSets: Record<string, number>[] = [];
    
    // For simplicity, generate all combinations (Cartesian product)
    const generateCombinations = (params: SweepParameter[], index: number, current: Record<string, number>) => {
      if (index >= params.length) {
        parameterSets.push({ ...current });
        return;
      }

      const param = params[index];
      const values = this.generateParameterValues(param);

      for (const value of values) {
        current[param.name] = value;
        generateCombinations(params, index + 1, current);
      }
    };

    generateCombinations(sweepParams, 0, {});
    return parameterSets;
  }

  /**
   * Generate values for a sweep parameter
   */
  private generateParameterValues(param: SweepParameter): number[] {
    const values: number[] = [];
    
    if (param.stepType === 'linear') {
      for (let value = param.startValue; value <= param.endValue; value += param.stepSize) {
        values.push(value);
      }
    } else if (param.stepType === 'logarithmic') {
      let value = param.startValue;
      while (value <= param.endValue) {
        values.push(value);
        value *= param.stepSize;
      }
    }

    return values;
  }

  /**
   * Apply parameter set to device
   */
  private async applyParameterSet(paramSet: Record<string, number>): Promise<void> {
    for (const [param, value] of Object.entries(paramSet)) {
      // Map parameter names to SCPI commands
      const command = this.mapParameterToCommand(param, value);
      if (command) {
        await this.generator.sendCommand(command);
      }
    }
  }

  /**
   * Map parameter name to SCPI command
   */
  private mapParameterToCommand(param: string, value: number): string | null {
    const parameterMap: Record<string, string> = {
      'level': `TEST:CWG:LEVEL:VAL ${value}`,
      'frequency': `TEST:CWG:FREQUENCY:VAL ${value}`,
      'duration': `TEST:CWG:DURATION:VAL ${value}`,
      // Add more parameter mappings as needed
    };

    return parameterMap[param] || null;
  }

  /**
   * Check stop conditions
   */
  private checkStopConditions(conditions: StopCondition[], results: any[]): boolean {
    for (const condition of conditions) {
      switch (condition.type) {
        case 'error':
          if (results.some(r => r.error)) {
            return true;
          }
          break;
        case 'threshold':
          // Implement threshold checking logic
          break;
        case 'time':
          // Implement time-based stopping
          break;
      }
    }
    return false;
  }

  /**
   * Collect additional measurement data
   */
  private async collectData(config: DataCollectionConfig): Promise<any> {
    const data: any = {};

    for (const measurement of config.measurements) {
      try {
        const command = this.mapMeasurementToCommand(measurement);
        if (command) {
          data[measurement] = await this.generator.sendCommand(command);
        }
      } catch (error) {
        data[measurement] = null;
      }
    }

    return data;
  }

  /**
   * Map measurement name to SCPI command
   */
  private mapMeasurementToCommand(measurement: string): string | null {
    const measurementMap: Record<string, string> = {
      'voltage': 'PWR:MEAS:1:VOLTAGE?',
      'current': 'PWR:MEAS:1:CURRENT?',
      'temperature': 'ENVIRONMENT:TEMPERATURE?',
      'humidity': 'ENVIRONMENT:HUMIDITY?',
      // Add more measurement mappings as needed
    };

    return measurementMap[measurement] || null;
  }
}