export interface DeviceConnection {
  id: string;
  name: string;
  ipAddress: string;
  port: number;
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  model: GeneratorModel;
  lastConnected?: Date;
}

export type GeneratorModel = 'IMU-4000' | 'IMU-3000' | 'IMU-MG1' | 'DOW-3000-S-F' | 'DOW-CG1';

export interface TestProfile {
  id: string;
  name: string;
  description: string;
  parameters: TestParameters;
  isCustom: boolean;
}

export interface TestParameters {
  couplingDevice: string;
  level: number;
  polarity: 'POSITIVE' | 'NEGATIVE' | 'BOTH';
  impedance: string;
  numberOfPulses: number;
  frequency?: number;
  duration?: number;
  waveform?: string;
}

export interface TestResult {
  id: string;
  timestamp: Date;
  profileName: string;
  parameters: TestParameters;
  status: 'success' | 'failed' | 'running' | 'stopped';
  duration: number;
  errorMessage?: string;
  data?: any;
}

export interface DeviceStatus {
  temperature: number;
  voltage: number;
  current: number;
  power: number;
  isReady: boolean;
  errorCount: number;
  lastError?: string;
}

export type UserRole = 'technician' | 'engineer';

export interface User {
  id: string;
  name: string;
  role: UserRole;
  permissions: string[];
}